/**
 * Super Admin Configuration Hook
 * 
 * React hook for managing database-driven super admin configuration.
 * Provides CRUD operations and real-time updates for super admin management.
 */

import { useState, useEffect, useCallback } from 'react';
import { SuperAdminRecord } from '@/lib/auth/super-admin-config';

/**
 * Super Admin Configuration Data
 */
export interface SuperAdminConfigData {
  records: (SuperAdminRecord & {
    source: 'environment' | 'database' | 'hybrid';
    canBeRemoved: boolean;
  })[];
  config: {
    totalCount: number;
    environmentCount: number;
    databaseCount: number;
    source: 'environment' | 'database' | 'hybrid';
  };
}

/**
 * Hook State
 */
interface UseSuperAdminConfigState {
  data: SuperAdminConfigData | null;
  loading: boolean;
  error: string | null;
}

/**
 * Hook Return Type
 */
export interface UseSuperAdminConfigReturn extends UseSuperAdminConfigState {
  // Data operations
  refresh: () => Promise<void>;
  
  // CRUD operations
  addSuperAdmin: (email: string, notes?: string) => Promise<{ success: boolean; error?: string }>;
  removeSuperAdmin: (email: string) => Promise<{ success: boolean; error?: string }>;
  reactivateSuperAdmin: (email: string) => Promise<{ success: boolean; error?: string }>;
  updateNotes: (email: string, notes: string) => Promise<{ success: boolean; error?: string }>;
  
  // Validation and audit
  validateConfiguration: () => Promise<{ success: boolean; data?: unknown; error?: string }>;
  getAuditLog: () => Promise<{ success: boolean; data?: unknown; error?: string }>;
}

/**
 * Super Admin Configuration Hook
 */
export function useSuperAdminConfig(): UseSuperAdminConfigReturn {
  const [state, setState] = useState<UseSuperAdminConfigState>({
    data: null,
    loading: true,
    error: null
  });
  
  /**
   * Fetch super admin configuration data
   */
  const fetchData = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      
      const response = await fetch('/api/admin/super-admin-config');
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch super admin configuration');
      }
      
      if (result.success) {
        setState({
          data: result.data,
          loading: false,
          error: null
        });
      } else {
        setState({
          data: null,
          loading: false,
          error: result.error || 'Unknown error'
        });
      }
    } catch (error) {
      console.error('Failed to fetch super admin configuration:', error);
      setState({
        data: null,
        loading: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }, []);
  
  /**
   * Refresh data
   */
  const refresh = useCallback(async () => {
    await fetchData();
  }, [fetchData]);
  
  /**
   * Add a new super admin
   */
  const addSuperAdmin = useCallback(async (email: string, notes?: string) => {
    try {
      const response = await fetch('/api/admin/super-admin-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, notes })
      });
      
      const result = await response.json();
      
      if (result.success) {
        // Refresh data after successful addition
        await fetchData();
      }
      
      return result;
    } catch (error) {
      console.error('Failed to add super admin:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }, [fetchData]);
  
  /**
   * Remove (deactivate) a super admin
   */
  const removeSuperAdmin = useCallback(async (email: string) => {
    try {
      const response = await fetch('/api/admin/super-admin-config', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, action: 'deactivate' })
      });
      
      const result = await response.json();
      
      if (result.success) {
        // Refresh data after successful removal
        await fetchData();
      }
      
      return result;
    } catch (error) {
      console.error('Failed to remove super admin:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }, [fetchData]);
  
  /**
   * Reactivate a super admin
   */
  const reactivateSuperAdmin = useCallback(async (email: string) => {
    try {
      const response = await fetch('/api/admin/super-admin-config', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, action: 'reactivate' })
      });
      
      const result = await response.json();
      
      if (result.success) {
        // Refresh data after successful reactivation
        await fetchData();
      }
      
      return result;
    } catch (error) {
      console.error('Failed to reactivate super admin:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }, [fetchData]);
  
  /**
   * Update super admin notes
   */
  const updateNotes = useCallback(async (email: string, notes: string) => {
    try {
      const response = await fetch('/api/admin/super-admin-config', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, action: 'update_notes', notes })
      });
      
      const result = await response.json();
      
      if (result.success) {
        // Refresh data after successful update
        await fetchData();
      }
      
      return result;
    } catch (error) {
      console.error('Failed to update super admin notes:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }, [fetchData]);
  
  /**
   * Validate configuration
   */
  const validateConfiguration = useCallback(async () => {
    try {
      const response = await fetch('/api/admin/super-admin-config?action=validate');
      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Failed to validate configuration:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }, []);
  
  /**
   * Get audit log
   */
  const getAuditLog = useCallback(async () => {
    try {
      const response = await fetch('/api/admin/super-admin-config?action=audit');
      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Failed to get audit log:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }, []);
  
  // Initial data fetch
  useEffect(() => {
    fetchData();
  }, [fetchData]);
  
  return {
    ...state,
    refresh,
    addSuperAdmin,
    removeSuperAdmin,
    reactivateSuperAdmin,
    updateNotes,
    validateConfiguration,
    getAuditLog
  };
}

export default useSuperAdminConfig;
