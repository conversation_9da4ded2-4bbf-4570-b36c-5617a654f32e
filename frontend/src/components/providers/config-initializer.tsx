'use client';

import { useEffect, useState } from 'react';
import { performSecurityConfigAudit, getSecurityConfigSummary } from '@/lib/config/security-config-validator';
import { getEnvironmentConfig } from '@/config/env';

// Client-safe super admin configuration function
function getClientSafeSuperAdminConfig() {
  const superAdminEmailsEnv = process.env.NEXT_PUBLIC_SUPER_ADMIN_EMAILS || process.env.SUPER_ADMIN_EMAILS;

  let emails: string[] = [];

  if (superAdminEmailsEnv) {
    try {
      // Try parsing as JSON array first
      emails = JSON.parse(superAdminEmailsEnv);
    } catch {
      // If JSON parsing fails, treat as comma-separated string
      emails = superAdminEmailsEnv.split(',').map(email => email.trim()).filter(email => email.length > 0);
    }
  }

  return {
    emails,
    source: emails.length > 0 ? 'environment' : 'none'
  };
}

interface StartupValidationResult {
  configValid: boolean;
  securityValid: boolean;
  superAdminValid: boolean;
  configError?: string;
  securityError?: string;
  superAdminError?: string;
  securitySummary?: string;
  criticalIssues: number;
  overallStatus: 'healthy' | 'warning' | 'critical';
  canProceed: boolean;
}

/**
 * Configuration Initializer Component
 *
 * Performs comprehensive security configuration validation during application startup.
 * This ensures that all security-critical configuration is properly validated
 * before the application becomes fully operational.
 *
 * Validation includes:
 * - Environment configuration validation
 * - Comprehensive security configuration audit
 * - Super admin configuration validation
 *
 * In production, critical security issues will block application startup.
 */
export function ConfigInitializer({ children }: { children: React.ReactNode }) {
  const [validationResult, setValidationResult] = useState<StartupValidationResult | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function performStartupValidation() {
      console.log('🔍 Starting comprehensive startup security validation...');
      const startTime = Date.now();

      const result: StartupValidationResult = {
        configValid: false,
        securityValid: false,
        superAdminValid: false,
        criticalIssues: 0,
        overallStatus: 'critical',
        canProceed: false
      };

      // 1. Validate basic environment configuration
      try {
        console.log('📋 Validating environment configuration...');
        getEnvironmentConfig();
        result.configValid = true;
        console.log('✅ Environment configuration validation passed');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown configuration error';
        console.error('❌ Environment configuration validation failed:', errorMessage);
        result.configError = errorMessage;
      }

      // 2. Validate super admin configuration (client-safe)
      try {
        console.log('👤 Validating super admin configuration...');

        // Use client-safe super admin configuration validation
        const config = getClientSafeSuperAdminConfig();
        const errors: string[] = [];

        if (!config.emails || config.emails.length === 0) {
          errors.push('No super admin emails configured');
        }

        // Validate email formats
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const invalidEmails = config.emails.filter(email => !emailRegex.test(email));
        if (invalidEmails.length > 0) {
          errors.push(`Invalid email formats: ${invalidEmails.join(', ')}`);
        }

        const isValid = errors.length === 0;

        if (isValid) {
          result.superAdminValid = true;
          console.log('✅ Super admin configuration validation passed');
        } else {
          result.superAdminValid = false;
          result.superAdminError = errors.join('; ');
          console.error('❌ Super admin configuration validation failed');
          errors.forEach(error => {
            console.error(`  - ${error}`);
          });
        }

        // Log configuration status for debugging (client-safe)
        console.log('Super Admin Configuration Status:');
        console.log(`✅ Environment emails: ${config.emails.length} configured`);
        console.log(`✅ Configuration source: ${config.source}`);
        console.log(`✅ Validation status: ${isValid ? 'VALID' : 'INVALID'}`);
        if (!isValid) {
          console.log(`❌ Errors: ${errors.join('; ')}`);
        }

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown super admin validation error';
        console.error('❌ Super admin configuration validation failed:', errorMessage);
        result.superAdminError = errorMessage;
      }

      // 3. Perform comprehensive security configuration audit
      try {
        console.log('🛡️ Performing comprehensive security configuration audit...');
        const securityAudit = await performSecurityConfigAudit();
        const securitySummary = getSecurityConfigSummary(securityAudit);

        result.criticalIssues = securityAudit.criticalIssues;
        result.securitySummary = securitySummary;

        // Determine security validation status
        if (securityAudit.overallStatus === 'healthy') {
          result.securityValid = true;
          console.log('✅ Security configuration audit passed');
        } else if (securityAudit.overallStatus === 'warning') {
          result.securityValid = true; // Allow startup with warnings
          console.warn('⚠️ Security configuration audit passed with warnings');
        } else {
          result.securityValid = false;
          result.securityError = `Critical security issues found: ${securityAudit.criticalIssues} critical issues must be resolved`;
          console.error('❌ Security configuration audit failed - critical issues found');
        }

        // Log detailed security audit results
        console.log('📊 Security Audit Summary:', {
          overallStatus: securityAudit.overallStatus,
          totalChecks: securityAudit.totalChecks,
          passedChecks: securityAudit.passedChecks,
          failedChecks: securityAudit.failedChecks,
          criticalIssues: securityAudit.criticalIssues,
          highIssues: securityAudit.highIssues
        });

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown security validation error';
        console.error('❌ Security configuration audit failed:', errorMessage);
        result.securityError = errorMessage;
        result.criticalIssues = 999; // Assume critical if audit fails
      }

      // 4. Determine overall startup status and whether to proceed
      const hasBlockingIssues = !result.configValid || !result.securityValid || !result.superAdminValid;
      const isProduction = process.env.NODE_ENV === 'production';

      if (hasBlockingIssues && isProduction) {
        result.overallStatus = 'critical';
        result.canProceed = false;
        console.error('🚨 PRODUCTION STARTUP BLOCKED - Critical security issues must be resolved');
      } else if (hasBlockingIssues && !isProduction) {
        result.overallStatus = 'critical';
        result.canProceed = true; // Allow development startup with warnings
        console.warn('⚠️ DEVELOPMENT STARTUP - Critical issues detected but allowing startup for development');
      } else if (result.criticalIssues > 0) {
        result.overallStatus = 'warning';
        result.canProceed = true;
        console.warn('⚠️ Startup validation completed with warnings - application starting with non-critical issues');
      } else {
        result.overallStatus = 'healthy';
        result.canProceed = true;
        console.log('🎉 Startup validation completed successfully - application ready');
      }

      const duration = Date.now() - startTime;
      console.log(`⏱️ Startup validation completed in ${duration}ms`);

      setValidationResult(result);
      setLoading(false);
    }

    performStartupValidation();
  }, []);

  // Show loading state during validation
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-lg font-medium">Validating Security Configuration</p>
          <p className="text-sm text-gray-500 mt-2">Performing comprehensive startup validation...</p>
        </div>
      </div>
    );
  }

  // Handle validation failure
  if (!validationResult) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center max-w-md p-6 bg-red-50 border border-red-200 rounded-lg">
          <h2 className="text-lg font-semibold text-red-800 mb-2">Validation Error</h2>
          <p className="text-red-600 mb-4">Failed to complete startup validation</p>
          <p className="text-sm text-red-500">
            Please check the console for detailed error information and refresh the page.
          </p>
        </div>
      </div>
    );
  }

  // Block startup for critical security issues in production
  if (!validationResult.canProceed) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center max-w-4xl p-8 bg-red-50 border border-red-200 rounded-lg">
          <div className="text-red-600 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-red-800 mb-4">🚨 Production Startup Blocked</h2>
          <p className="text-red-700 font-medium mb-6">
            Critical security configuration issues detected. Application startup has been blocked to prevent security vulnerabilities.
          </p>

          <div className="text-left bg-white p-6 rounded border mb-6 max-h-96 overflow-y-auto">
            <h3 className="font-semibold text-red-700 mb-4">Issues Found:</h3>

            {validationResult.configError && (
              <div className="mb-4 p-3 bg-red-50 rounded">
                <p className="text-sm font-medium text-red-600 mb-1">❌ Environment Configuration:</p>
                <p className="text-sm text-red-500">{validationResult.configError}</p>
              </div>
            )}

            {validationResult.superAdminError && (
              <div className="mb-4 p-3 bg-red-50 rounded">
                <p className="text-sm font-medium text-red-600 mb-1">❌ Super Admin Configuration:</p>
                <p className="text-sm text-red-500">{validationResult.superAdminError}</p>
              </div>
            )}

            {validationResult.securityError && (
              <div className="mb-4 p-3 bg-red-50 rounded">
                <p className="text-sm font-medium text-red-600 mb-1">❌ Security Configuration:</p>
                <p className="text-sm text-red-500">{validationResult.securityError}</p>
              </div>
            )}

            {validationResult.securitySummary && (
              <div className="mt-4 p-3 bg-gray-50 rounded">
                <p className="text-sm font-medium text-gray-700 mb-2">📋 Detailed Security Audit:</p>
                <pre className="whitespace-pre-wrap font-mono text-xs text-gray-600 max-h-48 overflow-y-auto">
                  {validationResult.securitySummary}
                </pre>
              </div>
            )}
          </div>

          <div className="text-center">
            <p className="text-red-600 font-medium mb-4">
              Please resolve all critical security issues and redeploy the application.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
            >
              Retry Validation
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Show warning banner for non-critical issues but allow startup
  if (validationResult.overallStatus === 'warning') {
    console.warn('⚠️ Application starting with security warnings - please review and resolve when possible');
  }

  return <>{children}</>;
}
