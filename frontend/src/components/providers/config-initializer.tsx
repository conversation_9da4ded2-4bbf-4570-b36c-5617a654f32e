'use client';

import { useEffect } from 'react';
import { logSuperAdminConfigStatus, validateSuperAdminConfig } from '@/lib/auth/super-admin-config';

/**
 * Configuration Initializer Component
 * 
 * Validates and logs configuration status during application startup.
 * This ensures that security-critical configuration is properly validated
 * before the application becomes fully operational.
 */
export function ConfigInitializer({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Validate super admin configuration on startup
    try {
      const validation = validateSuperAdminConfig();
      
      if (!validation.isValid) {
        console.error('CRITICAL: Super Admin Configuration Validation Failed');
        validation.errors.forEach(error => {
          console.error(`  - ${error}`);
        });
        
        // In production, this should potentially prevent app startup
        if (process.env.NODE_ENV === 'production') {
          console.error('Production deployment blocked due to invalid super admin configuration');
        }
      }
      
      // Log configuration status for debugging
      logSuperAdminConfigStatus();
      
    } catch (error) {
      console.error('Failed to validate super admin configuration:', error);
    }
  }, []);

  return <>{children}</>;
}
