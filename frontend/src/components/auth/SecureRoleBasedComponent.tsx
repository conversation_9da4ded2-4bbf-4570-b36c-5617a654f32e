'use client'

import React from 'react';
import { useSecurePermission } from '@/hooks/useSecurePermission';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';

interface SecureRoleBasedComponentProps {
  allowedRoles?: string[];
  feature?: 'templates' | 'documents' | 'cases' | 'admin';
  requireSuperAdmin?: boolean;
  showFallback?: boolean;
  fallback?: React.ReactNode;
  loadingComponent?: React.ReactNode;
  children: React.ReactNode;
}

/**
 * Secure role-based component that uses server-side validation
 * 
 * This component provides defense-in-depth security by:
 * 1. Using server-side permission validation that cannot be bypassed
 * 2. Showing loading states while checking permissions
 * 3. Providing secure fallback UI for unauthorized access
 * 4. Supporting both role-based and feature-based access control
 * 
 * Unlike the original RoleBasedComponent, this cannot be bypassed by:
 * - Disabling JavaScript (server-side validation)
 * - Manipulating client-side state (server validates from JWT)
 * - Direct URL access (permissions checked on every render)
 */
export function SecureRoleBasedComponent({
  allowedRoles,
  feature,
  requireSuperAdmin = false,
  showFallback = true,
  fallback = null,
  loadingComponent = null,
  children
}: SecureRoleBasedComponentProps) {
  const router = useRouter();
  
  const { hasAccess, isLoading, error } = useSecurePermission({
    roles: allowedRoles,
    feature,
    requireSuperAdmin
  });

  // Show loading state while checking permissions
  if (isLoading) {
    return loadingComponent || <SecureLoadingFallback />;
  }

  // If user has access, render children
  if (hasAccess) {
    return <>{children}</>;
  }

  // If no access and fallback is enabled, show fallback
  if (showFallback) {
    return <>{fallback || <SecureAccessDeniedFallback error={error} />}</>;
  }

  // Otherwise render nothing (secure default)
  return null;
}

/**
 * Default loading component while checking permissions
 */
function SecureLoadingFallback() {
  return (
    <div className="flex items-center justify-center p-8">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      <span className="ml-2 text-sm text-gray-600">Verifying permissions...</span>
    </div>
  );
}

/**
 * Default access denied component with security context
 */
function SecureAccessDeniedFallback({ error }: { error?: string | null }) {
  const router = useRouter();

  return (
    <div className="flex items-center justify-center p-8">
      <Card className="w-96">
        <CardHeader>
          <CardTitle className="flex items-center text-red-600">
            <ExclamationTriangleIcon className="h-5 w-5 mr-2" />
            Access Denied
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-700 mb-2">
            You don't have permission to access this content.
          </p>
          {error && (
            <p className="text-xs text-gray-500 mb-4 bg-gray-50 p-2 rounded">
              Reason: {error}
            </p>
          )}
          <p className="text-xs text-gray-500 mb-4">
            If you believe this is an error, please contact your administrator.
          </p>
          <Button 
            onClick={() => router.push('/dashboard')} 
            size="sm"
            className="w-full"
          >
            Return to Dashboard
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Convenience component for role-based access control
 */
export function SecureRoleGuard({ 
  allowedRoles, 
  children, 
  ...props 
}: Omit<SecureRoleBasedComponentProps, 'feature' | 'requireSuperAdmin'> & { allowedRoles: string[] }) {
  return (
    <SecureRoleBasedComponent allowedRoles={allowedRoles} {...props}>
      {children}
    </SecureRoleBasedComponent>
  );
}

/**
 * Convenience component for feature-based access control
 */
export function SecureFeatureGuard({ 
  feature, 
  children, 
  ...props 
}: Omit<SecureRoleBasedComponentProps, 'allowedRoles' | 'requireSuperAdmin'> & { feature: 'templates' | 'documents' | 'cases' | 'admin' }) {
  return (
    <SecureRoleBasedComponent feature={feature} {...props}>
      {children}
    </SecureRoleBasedComponent>
  );
}

/**
 * Convenience component for super admin access control
 */
export function SecureSuperAdminGuard({ 
  children, 
  ...props 
}: Omit<SecureRoleBasedComponentProps, 'allowedRoles' | 'feature' | 'requireSuperAdmin'>) {
  return (
    <SecureRoleBasedComponent requireSuperAdmin={true} {...props}>
      {children}
    </SecureRoleBasedComponent>
  );
}
