'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import {
  performSecurityConfigAudit,
  getSecurityConfigSummary,
  type SecurityConfigAudit,
  type ConfigValidationResult
} from '@/lib/config/security-config-validator';
import {
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw,
  Settings,
  Database,
  Key,
  Globe,
  Lock,
  User,
  Flag,
  ExternalLink,
  Info,
  AlertCircle
} from 'lucide-react';

interface SecurityConfigHealthDashboardProps {
  autoRefresh?: boolean;
  refreshInterval?: number; // in milliseconds
}

export function SecurityConfigHealthDashboard({ 
  autoRefresh = true, 
  refreshInterval = 30000 
}: SecurityConfigHealthDashboardProps) {
  const { toast } = useToast();
  const [audit, setAudit] = useState<SecurityConfigAudit | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);

  useEffect(() => {
    fetchSecurityConfigHealth();
    
    if (autoRefresh) {
      const interval = setInterval(fetchSecurityConfigHealth, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  async function fetchSecurityConfigHealth() {
    try {
      setLoading(true);

      // Try to fetch from backend API first, fallback to frontend validation
      try {
        const response = await fetch('/api/superadmin/security-config', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('supabase.auth.token')}` // Get token from localStorage
          }
        });

        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data) {
            setAudit(result.data.audit || result.data);
            setLastRefresh(new Date());
            return;
          }
        }
      } catch (apiError) {
        console.warn('Backend API not available, falling back to frontend validation:', apiError);
      }

      // Fallback to frontend security configuration audit
      const auditResult = await performSecurityConfigAudit();
      setAudit(auditResult);
      setLastRefresh(new Date());

    } catch (error: any) {
      console.error('Error fetching security configuration health:', error);
      toast({
        title: 'Error',
        description: 'Failed to load security configuration health data',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }

  async function handleRefresh() {
    setRefreshing(true);
    await fetchSecurityConfigHealth();
  }

  function getStatusIcon(status: string) {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'critical':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Info className="h-5 w-5 text-gray-500" />;
    }
  }

  function getStatusColor(status: string) {
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  }

  function getSeverityIcon(severity: string) {
    switch (severity) {
      case 'critical':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'high':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'medium':
        return <Info className="h-4 w-4 text-yellow-500" />;
      case 'low':
        return <Info className="h-4 w-4 text-blue-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  }

  function getCategoryIcon(category: string) {
    switch (category) {
      case 'Authentication':
        return <User className="h-4 w-4" />;
      case 'Database':
        return <Database className="h-4 w-4" />;
      case 'API Keys':
        return <Key className="h-4 w-4" />;
      case 'Security Headers':
        return <Shield className="h-4 w-4" />;
      case 'CORS':
        return <Globe className="h-4 w-4" />;
      case 'JWT':
        return <Lock className="h-4 w-4" />;
      case 'Super Admin':
        return <User className="h-4 w-4" />;
      case 'Feature Flags':
        return <Flag className="h-4 w-4" />;
      case 'External Services':
        return <ExternalLink className="h-4 w-4" />;
      default:
        return <Settings className="h-4 w-4" />;
    }
  }

  if (loading && !audit) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin text-primary mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">Loading security configuration health...</p>
        </div>
      </div>
    );
  }

  if (!audit) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">Failed to load security configuration health</p>
          <Button onClick={handleRefresh} className="mt-2" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with refresh controls */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            {getStatusIcon(audit.overallStatus)}
            Security Configuration Health
          </h2>
          <p className="text-sm text-muted-foreground mt-1">
            Real-time monitoring of security-critical configuration settings
            {lastRefresh && (
              <span className="ml-2">
                • Last updated: {lastRefresh.toLocaleTimeString()}
              </span>
            )}
          </p>
        </div>
        <Button 
          onClick={handleRefresh} 
          disabled={refreshing}
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          {refreshing ? 'Refreshing...' : 'Refresh'}
        </Button>
      </div>

      {/* Overall Status Card */}
      <Card className={`border-2 ${getStatusColor(audit.overallStatus)}`}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getStatusIcon(audit.overallStatus)}
            Overall Security Configuration Status
            <Badge className={getStatusColor(audit.overallStatus)}>
              {audit.overallStatus.toUpperCase()}
            </Badge>
          </CardTitle>
          <CardDescription>
            System-wide security configuration assessment
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{audit.passedChecks}</div>
              <div className="text-sm text-muted-foreground">Passed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{audit.failedChecks}</div>
              <div className="text-sm text-muted-foreground">Failed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-500">{audit.criticalIssues}</div>
              <div className="text-sm text-muted-foreground">Critical</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-500">{audit.highIssues}</div>
              <div className="text-sm text-muted-foreground">High Priority</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Configuration Health */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="categories">By Category</TabsTrigger>
          <TabsTrigger value="issues">Issues</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <SecurityConfigOverview audit={audit} />
        </TabsContent>

        <TabsContent value="categories" className="space-y-4">
          <SecurityConfigCategories 
            audit={audit} 
            getCategoryIcon={getCategoryIcon}
            getSeverityIcon={getSeverityIcon}
          />
        </TabsContent>

        <TabsContent value="issues" className="space-y-4">
          <SecurityConfigIssues 
            audit={audit}
            getSeverityIcon={getSeverityIcon}
            getCategoryIcon={getCategoryIcon}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Component for overview tab
function SecurityConfigOverview({ audit }: { audit: SecurityConfigAudit }) {
  const summary = getSecurityConfigSummary(audit);
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Configuration Audit Summary</CardTitle>
        <CardDescription>
          Comprehensive security configuration assessment report
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre className="whitespace-pre-wrap text-sm font-mono bg-muted p-4 rounded-lg overflow-auto max-h-96">
          {summary}
        </pre>
      </CardContent>
    </Card>
  );
}

// Component for categories tab
function SecurityConfigCategories({ 
  audit, 
  getCategoryIcon, 
  getSeverityIcon 
}: { 
  audit: SecurityConfigAudit;
  getCategoryIcon: (category: string) => React.ReactNode;
  getSeverityIcon: (severity: string) => React.ReactNode;
}) {
  return (
    <div className="grid gap-4">
      {Object.entries(audit.summary).map(([category, summary]) => (
        <Card key={category}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getCategoryIcon(category)}
              {category}
              <Badge variant={summary.failed > 0 ? 'destructive' : 'default'}>
                {summary.passed}/{summary.total}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-lg font-semibold text-green-600">{summary.passed}</div>
                <div className="text-sm text-muted-foreground">Passed</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-red-600">{summary.failed}</div>
                <div className="text-sm text-muted-foreground">Failed</div>
              </div>
              <div>
                <div className="text-lg font-semibold">{summary.total}</div>
                <div className="text-sm text-muted-foreground">Total</div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

// Component for issues tab
function SecurityConfigIssues({ 
  audit, 
  getSeverityIcon, 
  getCategoryIcon 
}: { 
  audit: SecurityConfigAudit;
  getSeverityIcon: (severity: string) => React.ReactNode;
  getCategoryIcon: (category: string) => React.ReactNode;
}) {
  const failedResults = audit.results.filter(result => !result.isValid);
  
  if (failedResults.length === 0) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-32">
          <div className="text-center">
            <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">No configuration issues found</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Configuration Issues ({failedResults.length})</CardTitle>
        <CardDescription>
          Security configuration issues requiring attention
        </CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Severity</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Configuration</TableHead>
              <TableHead>Issue</TableHead>
              <TableHead>Recommendation</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {failedResults.map((result, index) => (
              <TableRow key={index}>
                <TableCell>
                  <div className="flex items-center gap-2">
                    {getSeverityIcon(result.severity)}
                    <Badge variant={result.severity === 'critical' ? 'destructive' : 'secondary'}>
                      {result.severity.toUpperCase()}
                    </Badge>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    {getCategoryIcon(result.category)}
                    {result.category}
                  </div>
                </TableCell>
                <TableCell className="font-mono text-sm">{result.key}</TableCell>
                <TableCell className="text-sm">{result.message}</TableCell>
                <TableCell className="text-sm text-muted-foreground">
                  {result.recommendation || 'Review configuration documentation'}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
