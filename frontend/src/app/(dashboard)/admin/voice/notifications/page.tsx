'use client';

import React from 'react';
import { redirect } from 'next/navigation';
import { useAuth } from '@/lib/auth/useAuth';
import { useFeatures } from '@/hooks/useFeatures';
import { SecureRoleGuard } from '@/components/auth/SecureRoleBasedComponent';
import FailedNotificationsPanel from '@/components/voice/FailedNotificationsPanel';

export default function VoiceNotificationsPage() {
  const { hasFeature, isLoading } = useFeatures();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <SecureRoleGuard
      allowedRoles={['partner']}
      showFallback={true}
    >
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Failed Notifications</h1>
          <p className="text-muted-foreground">
            Monitor and manage failed voice system notifications and alerts
          </p>
        </div>

        <FailedNotificationsPanel />
      </div>
    </SecureRoleGuard>
  );
}
