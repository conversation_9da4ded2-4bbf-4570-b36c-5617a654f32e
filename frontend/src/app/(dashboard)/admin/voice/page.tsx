'use client';

import React from 'react';
import { redirect } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Phone, PhoneCall, AlertTriangle, Hash } from 'lucide-react';
import Link from 'next/link';
import { useAuth } from '@/lib/auth/useAuth';
import { useFeatures } from '@/hooks/useFeatures';
import QueueStatusWidget from '@/components/voice/QueueStatusWidget';
import { FeatureLock } from '@/components/subscription/feature-lock';
import { SecureRoleGuard } from '@/components/auth/SecureRoleBasedComponent';

export default function VoiceOverviewPage() {
  const { hasFeature, isLoading } = useFeatures();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  const voiceContent = (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Voice Receptionist</h1>
        <p className="text-muted-foreground">
          Manage your voice intake system, phone numbers, and call logs
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Call Logs</CardTitle>
            <PhoneCall className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">View & Manage</div>
            <p className="text-xs text-muted-foreground">
              Review incoming and outgoing calls
            </p>
            <Link href="/admin/voice/calls">
              <Button className="w-full mt-3" variant="outline">
                View Call Logs
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Phone Numbers</CardTitle>
            <Hash className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Purchase & Setup</div>
            <p className="text-xs text-muted-foreground">
              Search, buy, and configure numbers
            </p>
            <Link href="/admin/voice/numbers">
              <Button className="w-full mt-3" variant="outline">
                Manage Numbers
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Notifications</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Failed Alerts</div>
            <p className="text-xs text-muted-foreground">
              Monitor system notifications
            </p>
            <Link href="/admin/voice/notifications">
              <Button className="w-full mt-3" variant="outline">
                View Notifications
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Voice System</CardTitle>
            <Phone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Active</div>
            <p className="text-xs text-muted-foreground">
              System status and health
            </p>
            <Button className="w-full mt-3" variant="outline" disabled>
              System Healthy
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Queue Status Widget */}
      <div className="grid gap-6 md:grid-cols-2">
        <QueueStatusWidget />
        <div className="space-y-4">
          {/* Placeholder for future widgets */}
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common voice receptionist management tasks
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Link href="/admin/voice/numbers">
              <Button className="w-full" variant="default">
                <Hash className="mr-2 h-4 w-4" />
                Purchase New Number
              </Button>
            </Link>
            <Link href="/admin/voice/calls">
              <Button className="w-full" variant="outline">
                <PhoneCall className="mr-2 h-4 w-4" />
                View Recent Calls
              </Button>
            </Link>
            <Link href="/admin/voice/notifications">
              <Button className="w-full" variant="outline">
                <AlertTriangle className="mr-2 h-4 w-4" />
                Check Alerts
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <SecureRoleGuard
      allowedRoles={['partner']}
      showFallback={true}
    >
      <FeatureLock
        featureKey="voice_intake"
        title="Voice Receptionist"
        description="Upgrade to access the voice intake system, manage phone numbers, and review call logs."
        requiredPlan="Bundle"
        overlay={false}
      >
        {voiceContent}
      </FeatureLock>
    </SecureRoleGuard>
  );
}
