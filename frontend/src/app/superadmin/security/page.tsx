'use client'

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { SecurityEventCategory } from '@/app/api/security/log/route'
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch'
import {
  AlertTriangle,
  Bell,
  Shield,
  Key,
  Activity,
  Users,
  Settings,
  RefreshCw
} from 'lucide-react'
import { AnomalyDashboard } from '@/components/security/anomaly-dashboard'
import { AlertsDashboard } from '@/components/security/alerts-dashboard'
import { TokensDashboard } from '@/components/security/tokens-dashboard'
import { SecurityConfigHealthDashboard } from '@/components/superadmin/security-config-health-dashboard'

interface SecurityEvent {
  id: string
  event_type: string
  event_category: string
  user_id: string
  ip_address: string
  user_agent: string
  location_city: string | null
  location_region: string | null
  location_country: string | null
  details: any
  created_at: string
}

// Define expected API response structure
interface SecurityEventsApiResponse {
  data: SecurityEvent[];
  error?: string;
}

export default function SecurityDashboard() {
  const [events, setEvents] = useState<SecurityEvent[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('all')
  const router = useRouter()
  const { authedFetch } = useAuthenticatedFetch()

  useEffect(() => {
    // SECURITY: Access control is handled by SuperAdminGuard in layout
    // No need for client-side session checks that can be bypassed
    fetchEvents()
  }, [])

  async function fetchEvents() {
    setLoading(true)
    try {
      console.log('Fetching security events...')

      // Use the dedicated API endpoint instead of direct Supabase queries
      const url = new URL('/api/security/events', window.location.origin)

      // Add query parameters
      if (activeTab !== 'all') {
        url.searchParams.append('category', activeTab)
      }
      url.searchParams.append('limit', '100')

      // SECURITY: Access control is handled by SuperAdminGuard in layout
      // Superadmin users can see all events without filtering

      console.log('Fetching security events from:', url.toString())

      // Fetch the events from the API using the generic type
      const result = await authedFetch<SecurityEventsApiResponse>(url.toString())

      // Directly use the data from the result
      const data = result.data

      if (!data || data.length === 0) {
        console.log('No security events found, using mock data for demonstration')
        // Use mock data for demonstration if no events are found
        setEvents(createMockEvents())
        return
      }

      console.log('Fetched events:', data.length)
      setEvents(data)
    } catch (err: any) { // Catch errors thrown by authedFetch
      console.error('Error fetching security events:', err)
      // Use mock data for demonstration in case of error
      console.log('Using mock data due to fetch error')
      setEvents(createMockEvents())
    } finally {
      setLoading(false)
    }
  }

  function createMockEvents(): SecurityEvent[] {
    return [
      {
        id: 'mock-1',
        event_type: 'auth.login',
        event_category: 'authentication',
        user_id: 'mock-user-id',
        ip_address: '***********',
        user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
        location_city: 'San Francisco',
        location_region: 'California',
        location_country: 'United States',
        details: { action: 'login', success: true },
        created_at: new Date().toISOString()
      },
      {
        id: 'mock-2',
        event_type: 'suspicious.unusual_location',
        event_category: 'suspicious',
        user_id: 'mock-user-id',
        ip_address: '***********',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0)',
        location_city: 'Unknown',
        location_region: 'Unknown',
        location_country: 'Unknown',
        details: { reason: 'Unusual location detected' },
        created_at: new Date(Date.now() - 3600000).toISOString()
      }
    ]
  }

  useEffect(() => {
    fetchEvents()
  }, [activeTab])

  function getCategoryColor(category: string): string {
    switch (category) {
      case SecurityEventCategory.AUTHENTICATION:
        return 'bg-blue-500'
      case SecurityEventCategory.AUTHORIZATION:
        return 'bg-purple-500'
      case SecurityEventCategory.DATA_ACCESS:
        return 'bg-green-500'
      case SecurityEventCategory.SUSPICIOUS:
        return 'bg-red-500'
      case SecurityEventCategory.SYSTEM:
        return 'bg-gray-500'
      default:
        return 'bg-gray-400'
    }
  }

  function formatDate(dateString: string): string {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).format(date)
  }

  function truncateUserAgent(userAgent: string | null | undefined): string {
    if (!userAgent) {
      return 'N/A'; // Return 'N/A' if userAgent is null or undefined
    }
    return userAgent.length > 50 ? userAgent.substring(0, 47) + '...' : userAgent;
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Security Dashboard</h1>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-blue-500" />
              <CardTitle>Total Events</CardTitle>
            </div>
            <CardDescription>All security events</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-4xl font-bold">{events.length}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center space-x-2">
              <Key className="h-5 w-5 text-green-500" />
              <CardTitle>Authentication</CardTitle>
            </div>
            <CardDescription>Logins, logouts, etc.</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-4xl font-bold">
              {events.filter(e => e.event_category === SecurityEventCategory.AUTHENTICATION).length}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <CardTitle>Suspicious</CardTitle>
            </div>
            <CardDescription>Potential security issues</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-4xl font-bold text-red-500">
              {events.filter(e => e.event_category === SecurityEventCategory.SUSPICIOUS).length}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center space-x-2">
              <Bell className="h-5 w-5 text-yellow-500" />
              <CardTitle>Alerts</CardTitle>
            </div>
            <CardDescription>Security notifications</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-4xl font-bold text-yellow-500">
              {events.filter(e => e.event_type?.includes('alert')).length}
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="events">
        <div className="flex justify-between items-center mb-4">
          <TabsList className="grid grid-cols-5 w-full max-w-3xl">
            <TabsTrigger value="events" className="flex items-center">
              <Activity className="h-4 w-4 mr-2" />
              Events
            </TabsTrigger>
            <TabsTrigger value="config" className="flex items-center">
              <Settings className="h-4 w-4 mr-2" />
              Config
            </TabsTrigger>
            <TabsTrigger value="anomalies" className="flex items-center">
              <AlertTriangle className="h-4 w-4 mr-2" />
              Anomalies
            </TabsTrigger>
            <TabsTrigger value="alerts" className="flex items-center">
              <Bell className="h-4 w-4 mr-2" />
              Alerts
            </TabsTrigger>
            <TabsTrigger value="tokens" className="flex items-center">
              <Key className="h-4 w-4 mr-2" />
              Tokens
            </TabsTrigger>
          </TabsList>

          <Button onClick={fetchEvents} disabled={loading} size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            {loading ? 'Refreshing...' : 'Refresh'}
          </Button>
        </div>

        {/* Events Tab */}
        <TabsContent value="events" className="mt-0">
          <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
            <div className="flex items-center mb-4">
              <TabsList>
                <TabsTrigger value="all">All Events</TabsTrigger>
                <TabsTrigger value={SecurityEventCategory.AUTHENTICATION}>Authentication</TabsTrigger>
                <TabsTrigger value={SecurityEventCategory.AUTHORIZATION}>Authorization</TabsTrigger>
                <TabsTrigger value={SecurityEventCategory.DATA_ACCESS}>Data Access</TabsTrigger>
                <TabsTrigger value={SecurityEventCategory.SUSPICIOUS}>Suspicious</TabsTrigger>
                <TabsTrigger value={SecurityEventCategory.SYSTEM}>System</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="all" className="mt-0">
              <SecurityEventsTable
                events={events}
                getCategoryColor={getCategoryColor}
                formatDate={formatDate}
                truncateUserAgent={truncateUserAgent}
              />
            </TabsContent>

            {Object.values(SecurityEventCategory).map(category => (
              <TabsContent key={category} value={category} className="mt-0">
                <SecurityEventsTable
                  events={events.filter(e => e.event_category === category)}
                  getCategoryColor={getCategoryColor}
                  formatDate={formatDate}
                  truncateUserAgent={truncateUserAgent}
                />
              </TabsContent>
            ))}
          </Tabs>
        </TabsContent>

        {/* Security Configuration Health Tab */}
        <TabsContent value="config" className="mt-0">
          <SecurityConfigHealthDashboard autoRefresh={true} refreshInterval={30000} />
        </TabsContent>

        {/* Anomalies Tab */}
        <TabsContent value="anomalies" className="mt-0">
          <AnomalyDashboard />
        </TabsContent>

        {/* Alerts Tab */}
        <TabsContent value="alerts" className="mt-0">
          <AlertsDashboard />
        </TabsContent>

        {/* Tokens Tab */}
        <TabsContent value="tokens" className="mt-0">
          <TokensDashboard />
        </TabsContent>
      </Tabs>
    </div>
  )
}

interface SecurityEventsTableProps {
  events: SecurityEvent[]
  getCategoryColor: (category: string) => string
  formatDate: (dateString: string) => string
  truncateUserAgent: (userAgent: string | null | undefined) => string
}

function SecurityEventsTable({
  events,
  getCategoryColor,
  formatDate,
  truncateUserAgent
}: SecurityEventsTableProps) {
  if (events.length === 0) {
    return <div className="text-center py-8">No events found</div>
  }

  return (
    <Card>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Time</TableHead>
              <TableHead>Event Type</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>User ID</TableHead>
              <TableHead>Location</TableHead>
              <TableHead>IP Address</TableHead>
              <TableHead>User Agent</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {events.map(event => (
              <TableRow key={event.id}>
                <TableCell className="font-mono text-xs">
                  {formatDate(event.created_at)}
                </TableCell>
                <TableCell>{event.event_type}</TableCell>
                <TableCell>
                  <Badge className={`${getCategoryColor(event.event_category)} text-white`}>
                    {event.event_category}
                  </Badge>
                </TableCell>
                <TableCell className="font-mono text-xs">
                  {event.user_id ? event.user_id.substring(0, 8) + '...' : 'N/A'}
                </TableCell>
                <TableCell>
                  {event.location_city && event.location_country
                    ? `${typeof event.location_city === 'string' ? event.location_city : JSON.stringify(event.location_city)}, ${typeof event.location_country === 'string' ? event.location_country : JSON.stringify(event.location_country)}`
                    : 'Unknown'}
                </TableCell>
                <TableCell className="font-mono text-xs">{event.ip_address}</TableCell>
                <TableCell className="text-xs">
                  {truncateUserAgent(event.user_agent)}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
