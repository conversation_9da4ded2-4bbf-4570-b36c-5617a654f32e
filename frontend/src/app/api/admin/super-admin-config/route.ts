/**
 * Super Admin Configuration API
 * 
 * API endpoints for managing database-driven super admin configuration.
 * Provides secure CRUD operations for super admin management.
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { isSuperAdminEmailAsync } from '@/lib/auth/super-admin-config';
import SuperAdminService from '@/lib/services/super-admin-service';

/**
 * Validate that the requesting user is a super admin
 */
async function validateSuperAdminAccess(request: NextRequest): Promise<{
  isValid: boolean;
  userEmail?: string;
  error?: string;
}> {
  try {
    const supabase = createClient();
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      return { isValid: false, error: 'Authentication required' };
    }
    
    const userEmail = user.email;
    if (!userEmail) {
      return { isValid: false, error: 'User email not found' };
    }
    
    const isSuperAdmin = await isSuperAdminEmailAsync(userEmail);
    if (!isSuperAdmin) {
      return { isValid: false, error: 'Super admin access required' };
    }
    
    return { isValid: true, userEmail };
  } catch (error) {
    console.error('Super admin access validation error:', error);
    return { 
      isValid: false, 
      error: 'Access validation failed' 
    };
  }
}

/**
 * GET /api/admin/super-admin-config
 * Get all super admin configuration
 */
export async function GET(request: NextRequest) {
  try {
    // Validate super admin access
    const validation = await validateSuperAdminAccess(request);
    if (!validation.isValid) {
      return NextResponse.json(
        { error: validation.error },
        { status: validation.error === 'Authentication required' ? 401 : 403 }
      );
    }
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    
    switch (action) {
      case 'audit':
        const auditResult = await SuperAdminService.getSuperAdminAuditLog();
        return NextResponse.json(auditResult);
        
      case 'validate':
        const validationResult = await SuperAdminService.validateConfiguration();
        return NextResponse.json(validationResult);
        
      default:
        const result = await SuperAdminService.getAllSuperAdmins();
        return NextResponse.json(result);
    }
  } catch (error) {
    console.error('Super admin config GET error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/super-admin-config
 * Add a new super admin
 */
export async function POST(request: NextRequest) {
  try {
    // Validate super admin access
    const validation = await validateSuperAdminAccess(request);
    if (!validation.isValid) {
      return NextResponse.json(
        { error: validation.error },
        { status: validation.error === 'Authentication required' ? 401 : 403 }
      );
    }
    
    // Parse request body
    const body = await request.json();
    const { email, notes } = body;
    
    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }
    
    // Add super admin
    const result = await SuperAdminService.addSuperAdmin(
      email,
      validation.userEmail!,
      notes
    );
    
    return NextResponse.json(result, {
      status: result.success ? 200 : 400
    });
  } catch (error) {
    console.error('Super admin config POST error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/admin/super-admin-config
 * Update super admin configuration
 */
export async function PATCH(request: NextRequest) {
  try {
    // Validate super admin access
    const validation = await validateSuperAdminAccess(request);
    if (!validation.isValid) {
      return NextResponse.json(
        { error: validation.error },
        { status: validation.error === 'Authentication required' ? 401 : 403 }
      );
    }
    
    // Parse request body
    const body = await request.json();
    const { email, action, notes } = body;
    
    if (!email || !action) {
      return NextResponse.json(
        { error: 'Email and action are required' },
        { status: 400 }
      );
    }
    
    let result;
    
    switch (action) {
      case 'deactivate':
        result = await SuperAdminService.removeSuperAdmin(
          email,
          validation.userEmail!
        );
        break;
        
      case 'reactivate':
        result = await SuperAdminService.reactivateSuperAdmin(
          email,
          validation.userEmail!
        );
        break;
        
      case 'update_notes':
        if (notes === undefined) {
          return NextResponse.json(
            { error: 'Notes are required for update_notes action' },
            { status: 400 }
          );
        }
        result = await SuperAdminService.updateSuperAdminNotes(
          email,
          notes,
          validation.userEmail!
        );
        break;
        
      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: deactivate, reactivate, update_notes' },
          { status: 400 }
        );
    }
    
    return NextResponse.json(result, {
      status: result.success ? 200 : 400
    });
  } catch (error) {
    console.error('Super admin config PATCH error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/super-admin-config
 * This endpoint is disabled - use PATCH with action=deactivate instead
 */
export async function DELETE(request: NextRequest) {
  return NextResponse.json(
    { 
      error: 'Direct deletion not allowed. Use PATCH with action=deactivate instead for audit trail.' 
    },
    { status: 405 }
  );
}
