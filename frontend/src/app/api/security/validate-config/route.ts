import { NextRequest, NextResponse } from 'next/server';
import { performSecurityConfigAudit, getSecurityConfigSummary } from '@/lib/config/security-config-validator';
import { getEnvironmentConfig } from '@/config/env';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Starting server-side security configuration validation...');
    const startTime = Date.now();

    const result = {
      configValid: false,
      securityValid: false,
      superAdminValid: false,
      criticalIssues: 0,
      overallStatus: 'critical' as 'healthy' | 'warning' | 'critical',
      canProceed: false,
      configError: undefined as string | undefined,
      securityError: undefined as string | undefined,
      superAdminError: undefined as string | undefined,
      securitySummary: undefined as string | undefined,
    };

    // 1. Validate basic environment configuration
    try {
      console.log('📋 Validating environment configuration...');
      getEnvironmentConfig();
      result.configValid = true;
      console.log('✅ Environment configuration validation passed');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown configuration error';
      console.error('❌ Environment configuration validation failed:', errorMessage);
      result.configError = errorMessage;
    }

    // 2. Validate super admin configuration (server-side)
    try {
      console.log('👤 Validating super admin configuration...');

      // Server-side super admin configuration validation
      const superAdminEmailsEnv = process.env.SUPER_ADMIN_EMAILS || process.env.NEXT_PUBLIC_SUPER_ADMIN_EMAILS;
      const errors: string[] = [];

      let emails: string[] = [];
      if (superAdminEmailsEnv) {
        try {
          // Try parsing as JSON array first
          emails = JSON.parse(superAdminEmailsEnv);
        } catch {
          // If JSON parsing fails, treat as comma-separated string
          emails = superAdminEmailsEnv.split(',').map(email => email.trim()).filter(email => email.length > 0);
        }
      }

      if (!emails || emails.length === 0) {
        errors.push('No super admin emails configured');
      }

      // Validate email formats
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const invalidEmails = emails.filter(email => !emailRegex.test(email));
      if (invalidEmails.length > 0) {
        errors.push(`Invalid email formats: ${invalidEmails.join(', ')}`);
      }

      const isValid = errors.length === 0;

      if (isValid) {
        result.superAdminValid = true;
        console.log('✅ Super admin configuration validation passed');
      } else {
        result.superAdminValid = false;
        result.superAdminError = errors.join('; ');
        console.error('❌ Super admin configuration validation failed');
        errors.forEach(error => {
          console.error(`  - ${error}`);
        });
      }

      // Log configuration status for debugging
      console.log('Super Admin Configuration Status:');
      console.log(`✅ Environment emails: ${emails.length} configured`);
      console.log(`✅ Configuration source: ${emails.length > 0 ? 'environment' : 'none'}`);
      console.log(`✅ Validation status: ${isValid ? 'VALID' : 'INVALID'}`);
      if (!isValid) {
        console.log(`❌ Errors: ${errors.join('; ')}`);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown super admin validation error';
      console.error('❌ Super admin configuration validation failed:', errorMessage);
      result.superAdminError = errorMessage;
    }

    // 3. Perform comprehensive security configuration audit
    try {
      console.log('🛡️ Performing comprehensive security configuration audit...');
      const securityAudit = await performSecurityConfigAudit();
      const securitySummary = getSecurityConfigSummary(securityAudit);

      result.criticalIssues = securityAudit.criticalIssues;
      result.securitySummary = securitySummary;

      // Determine security validation status
      if (securityAudit.overallStatus === 'healthy') {
        result.securityValid = true;
        console.log('✅ Security configuration audit passed');
      } else if (securityAudit.overallStatus === 'warning') {
        result.securityValid = true; // Allow startup with warnings
        console.warn('⚠️ Security configuration audit passed with warnings');
      } else {
        result.securityValid = false;
        result.securityError = `Critical security issues found: ${securityAudit.criticalIssues} critical issues must be resolved`;
        console.error('❌ Security configuration audit failed - critical issues found');
      }

      // Log detailed security audit results
      console.log('📊 Security Audit Summary:', {
        overallStatus: securityAudit.overallStatus,
        totalChecks: securityAudit.totalChecks,
        passedChecks: securityAudit.passedChecks,
        failedChecks: securityAudit.failedChecks,
        criticalIssues: securityAudit.criticalIssues,
        highIssues: securityAudit.highIssues
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown security validation error';
      console.error('❌ Security configuration audit failed:', errorMessage);
      result.securityError = errorMessage;
      result.criticalIssues = 999; // Assume critical if audit fails
    }

    // 4. Determine overall startup status and whether to proceed
    const hasBlockingIssues = !result.configValid || !result.securityValid || !result.superAdminValid;
    const isProduction = process.env.NODE_ENV === 'production';

    if (hasBlockingIssues && isProduction) {
      result.overallStatus = 'critical';
      result.canProceed = false;
      console.error('🚨 PRODUCTION STARTUP BLOCKED - Critical security issues must be resolved');
    } else if (hasBlockingIssues && !isProduction) {
      result.overallStatus = 'critical';
      result.canProceed = true; // Allow development startup with warnings
      console.warn('⚠️ DEVELOPMENT STARTUP - Critical issues detected but allowing startup for development');
    } else if (result.criticalIssues > 0) {
      result.overallStatus = 'warning';
      result.canProceed = true;
      console.warn('⚠️ Startup validation completed with warnings - application starting with non-critical issues');
    } else {
      result.overallStatus = 'healthy';
      result.canProceed = true;
      console.log('🎉 Startup validation completed successfully - application ready');
    }

    const duration = Date.now() - startTime;
    console.log(`⏱️ Startup validation completed in ${duration}ms`);

    return NextResponse.json(result);

  } catch (error) {
    console.error('❌ Security configuration validation API failed:', error);
    
    return NextResponse.json(
      {
        configValid: false,
        securityValid: false,
        superAdminValid: false,
        criticalIssues: 999,
        overallStatus: 'critical',
        canProceed: false,
        configError: 'Security validation API failed',
        securityError: error instanceof Error ? error.message : 'Unknown API error',
        superAdminError: undefined,
        securitySummary: undefined,
      },
      { status: 500 }
    );
  }
}