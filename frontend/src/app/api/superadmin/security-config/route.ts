import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { headers } from 'next/headers';

// Import the backend security configuration validator
const BACKEND_API_URL = process.env.LANGGRAPH_API_URL || 'http://localhost:8000';

/**
 * GET /api/superadmin/security-config
 * 
 * Fetches security configuration health data from the backend
 * Requires superadmin authentication
 */
export async function GET(request: NextRequest) {
  try {
    // Get the authorization header
    const headersList = headers();
    const authorization = headersList.get('authorization');
    
    if (!authorization) {
      return NextResponse.json(
        { error: 'Authorization header required' },
        { status: 401 }
      );
    }

    // Verify superadmin access using Supabase
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Extract JWT token from authorization header
    const token = authorization.replace('Bearer ', '');
    
    // Verify the JWT token
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      );
    }

    // Check if user is superadmin
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('is_super_admin')
      .eq('user_id', user.id)
      .single();

    if (profileError || !profile?.is_super_admin) {
      return NextResponse.json(
        { error: 'Superadmin access required' },
        { status: 403 }
      );
    }

    // Fetch security configuration audit from backend
    const backendResponse = await fetch(`${BACKEND_API_URL}/api/security/config/audit`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authorization, // Forward the authorization header
      },
    });

    if (!backendResponse.ok) {
      const errorText = await backendResponse.text();
      console.error('Backend security config audit failed:', errorText);
      return NextResponse.json(
        { error: 'Failed to fetch security configuration audit from backend' },
        { status: backendResponse.status }
      );
    }

    const auditData = await backendResponse.json();

    return NextResponse.json({
      success: true,
      data: auditData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in security config API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/superadmin/security-config/validate
 * 
 * Triggers a fresh security configuration validation
 * Requires superadmin authentication
 */
export async function POST(request: NextRequest) {
  try {
    // Get the authorization header
    const headersList = headers();
    const authorization = headersList.get('authorization');
    
    if (!authorization) {
      return NextResponse.json(
        { error: 'Authorization header required' },
        { status: 401 }
      );
    }

    // Verify superadmin access using Supabase
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Extract JWT token from authorization header
    const token = authorization.replace('Bearer ', '');
    
    // Verify the JWT token
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      );
    }

    // Check if user is superadmin
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('is_super_admin')
      .eq('user_id', user.id)
      .single();

    if (profileError || !profile?.is_super_admin) {
      return NextResponse.json(
        { error: 'Superadmin access required' },
        { status: 403 }
      );
    }

    // Trigger fresh security configuration validation on backend
    const backendResponse = await fetch(`${BACKEND_API_URL}/api/security/config/validate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authorization, // Forward the authorization header
      },
    });

    if (!backendResponse.ok) {
      const errorText = await backendResponse.text();
      console.error('Backend security config validation failed:', errorText);
      return NextResponse.json(
        { error: 'Failed to trigger security configuration validation on backend' },
        { status: backendResponse.status }
      );
    }

    const validationData = await backendResponse.json();

    return NextResponse.json({
      success: true,
      data: validationData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in security config validation API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
