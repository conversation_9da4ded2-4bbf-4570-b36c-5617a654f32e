import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { isSuperAdminEmailAsync } from '@/lib/auth/super-admin-config';

// Import the backend security configuration validator
const BACKEND_API_URL = process.env.LANGGRAPH_API_URL || 'http://localhost:8000';

/**
 * Validate that the requesting user is a super admin
 */
async function validateSuperAdminAccess(request: NextRequest): Promise<{
  isValid: boolean;
  userEmail?: string;
  error?: string;
}> {
  try {
    const supabase = createClient();
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error || !user) {
      return { isValid: false, error: 'Authentication required' };
    }

    const userEmail = user.email;
    if (!userEmail) {
      return { isValid: false, error: 'User email not found' };
    }

    const isSuperAdmin = await isSuperAdminEmailAsync(userEmail);
    if (!isSuperAdmin) {
      return { isValid: false, error: 'Super admin access required' };
    }

    return { isValid: true, userEmail };
  } catch (error) {
    console.error('Super admin access validation error:', error);
    return {
      isValid: false,
      error: 'Access validation failed'
    };
  }
}

/**
 * GET /api/superadmin/security-config
 *
 * Fetches security configuration health data from the backend
 * Requires superadmin authentication
 */
export async function GET(request: NextRequest) {
  try {
    // Validate super admin access
    const validation = await validateSuperAdminAccess(request);
    if (!validation.isValid) {
      return NextResponse.json(
        { error: validation.error },
        { status: validation.error === 'Authentication required' ? 401 : 403 }
      );
    }

    // Get authorization header from request
    const authorization = request.headers.get('authorization') || '';

    // Fetch security configuration audit from backend
    const backendResponse = await fetch(`${BACKEND_API_URL}/api/security/config/audit`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authorization, // Forward the authorization header
      },
    });

    if (!backendResponse.ok) {
      const errorText = await backendResponse.text();
      console.error('Backend security config audit failed:', errorText);
      return NextResponse.json(
        { error: 'Failed to fetch security configuration audit from backend' },
        { status: backendResponse.status }
      );
    }

    const auditData = await backendResponse.json();

    return NextResponse.json({
      success: true,
      data: auditData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in security config API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/superadmin/security-config/validate
 *
 * Triggers a fresh security configuration validation
 * Requires superadmin authentication
 */
export async function POST(request: NextRequest) {
  try {
    // Validate super admin access
    const validation = await validateSuperAdminAccess(request);
    if (!validation.isValid) {
      return NextResponse.json(
        { error: validation.error },
        { status: validation.error === 'Authentication required' ? 401 : 403 }
      );
    }

    // Get authorization header from request
    const authorization = request.headers.get('authorization') || '';

    // Trigger fresh security configuration validation on backend
    const backendResponse = await fetch(`${BACKEND_API_URL}/api/security/config/validate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authorization, // Forward the authorization header
      },
    });

    if (!backendResponse.ok) {
      const errorText = await backendResponse.text();
      console.error('Backend security config validation failed:', errorText);
      return NextResponse.json(
        { error: 'Failed to trigger security configuration validation on backend' },
        { status: backendResponse.status }
      );
    }

    const validationData = await backendResponse.json();

    return NextResponse.json({
      success: true,
      data: validationData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in security config validation API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
