import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

// Schema for permission check request
const permissionCheckSchema = z.object({
  roles: z.array(z.string()).optional(),
  feature: z.enum(['templates', 'documents', 'cases', 'admin']).optional(),
  requireSuperAdmin: z.boolean().optional(),
});

type PermissionCheckRequest = z.infer<typeof permissionCheckSchema>;

/**
 * POST /api/auth/check-permission
 * 
 * Server-side permission validation endpoint that cannot be bypassed by client-side manipulation.
 * This provides secure authorization checks for client components.
 * 
 * Request body:
 * - roles?: string[] - Array of roles to check against
 * - feature?: string - Feature access to check
 * - requireSuperAdmin?: boolean - Whether super admin access is required
 * 
 * Response:
 * - hasAccess: boolean - Whether the user has the requested permission
 * - userRole: string | null - The user's current role
 * - isSuperAdmin: boolean - Whether the user is a super admin
 * - reason?: string - Reason for denial (if hasAccess is false)
 */
export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    let requestBody: PermissionCheckRequest;
    try {
      const body = await request.json();
      requestBody = permissionCheckSchema.parse(body);
    } catch (error) {
      return NextResponse.json(
        { 
          error: 'Invalid request format',
          hasAccess: false,
          reason: 'Invalid request parameters'
        }, 
        { status: 400 }
      );
    }

    // Create Supabase client and get user session
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { 
          error: 'Unauthorized',
          hasAccess: false,
          reason: 'User not authenticated'
        }, 
        { status: 401 }
      );
    }

    // Extract user role from metadata (server-side source of truth)
    const userRole = user.app_metadata?.role || user.user_metadata?.role || null;
    
    // Check super admin status (server-side source of truth)
    const isSuperAdmin = user.app_metadata?.is_super_admin === true || 
                        user.user_metadata?.is_super_admin === true ||
                        user.email === '<EMAIL>'; // Fallback check

    // If super admin access is required
    if (requestBody.requireSuperAdmin) {
      if (!isSuperAdmin) {
        return NextResponse.json({
          hasAccess: false,
          userRole,
          isSuperAdmin,
          reason: 'Super admin access required'
        });
      }
      
      return NextResponse.json({
        hasAccess: true,
        userRole,
        isSuperAdmin
      });
    }

    // Check role-based access
    if (requestBody.roles && requestBody.roles.length > 0) {
      if (!userRole || !requestBody.roles.includes(userRole)) {
        return NextResponse.json({
          hasAccess: false,
          userRole,
          isSuperAdmin,
          reason: `User role '${userRole}' not in allowed roles: ${requestBody.roles.join(', ')}`
        });
      }
    }

    // Check feature-based access
    if (requestBody.feature) {
      const hasFeatureAccess = checkFeatureAccess(userRole, requestBody.feature);
      if (!hasFeatureAccess) {
        return NextResponse.json({
          hasAccess: false,
          userRole,
          isSuperAdmin,
          reason: `User role '${userRole}' does not have access to feature '${requestBody.feature}'`
        });
      }
    }

    // If we get here, user has access
    return NextResponse.json({
      hasAccess: true,
      userRole,
      isSuperAdmin
    });

  } catch (error) {
    console.error('Error in permission check:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        hasAccess: false,
        reason: 'Server error during permission check'
      }, 
      { status: 500 }
    );
  }
}

/**
 * Server-side feature access logic (matches client-side logic but cannot be bypassed)
 */
function checkFeatureAccess(userRole: string | null, feature: string): boolean {
  if (!userRole) return false;

  switch (feature) {
    case 'templates':
      return ['partner', 'attorney', 'staff'].includes(userRole);
    case 'documents':
      return ['partner', 'attorney', 'paralegal', 'staff', 'client'].includes(userRole);
    case 'cases':
      return ['partner', 'attorney', 'paralegal', 'staff'].includes(userRole);
    case 'admin':
      return userRole === 'partner';
    default:
      return false;
  }
}
