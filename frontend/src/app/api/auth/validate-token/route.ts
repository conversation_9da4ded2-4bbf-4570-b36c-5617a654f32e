/**
 * Secure Server-Side JWT Validation API
 * Replaces client-side JWT parsing with secure server-side validation
 */

import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '@/lib/supabase/database.types';

export interface SecureTokenValidationResponse {
  valid: boolean;
  user?: {
    id: string;
    email: string;
    role: string;
    tenant_id: string;
    is_super_admin?: boolean;
  };
  error?: string;
  expires_at?: string;
}

export async function POST(request: NextRequest): Promise<NextResponse<SecureTokenValidationResponse>> {
  try {
    const { token } = await request.json();
    
    if (!token) {
      return NextResponse.json({
        valid: false,
        error: 'No token provided'
      }, { status: 400 });
    }

    // Create server-side Supabase client
    const cookieStore = cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Validate token with Supabase Auth (server-side verification)
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      return NextResponse.json({
        valid: false,
        error: 'Invalid or expired token'
      }, { status: 401 });
    }

    // Get user profile for additional claims (role, tenant_id)
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role, tenant_id, is_super_admin')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.warn('Failed to fetch user profile:', profileError);
    }

    // Return validated user information
    return NextResponse.json({
      valid: true,
      user: {
        id: user.id,
        email: user.email || '',
        role: profile?.role || 'client',
        tenant_id: profile?.tenant_id || user.id, // Fallback to user ID
        is_super_admin: profile?.is_super_admin || false,
      },
      expires_at: user.email_confirmed_at // Use a safe timestamp field
    });

  } catch (error) {
    console.error('Token validation error:', error);
    return NextResponse.json({
      valid: false,
      error: 'Token validation failed'
    }, { status: 500 });
  }
}
