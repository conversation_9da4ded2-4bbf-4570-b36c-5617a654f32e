/**
 * Helper functions for API routes that use Supabase
 * This file provides utilities for working with Supabase in API routes
 *
 * NOTE: This file is deprecated. Use api-helpers.ts instead.
 * It is kept for backward compatibility.
 */

import { NextRequest, NextResponse } from 'next/server';
import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '@/lib/supabase/database.types';
import { createTypedDatabaseClient, TypedDatabaseClient } from './typed-client';
import { AuthUser, UserRole, isValidUserRole } from '@/lib/types/auth';
import { SupabaseClient, Session } from '@supabase/supabase-js';
// SECURITY: Removed parseJwtPayload import - using secure server-side validation instead
import { logSecurityEvent } from '../security/forensics'; // Import logSecurityEvent instead

/**
 * Creates an AuthUser object from a Supabase Session using secure server-side validation.
 * SECURITY: Removed client-side JWT parsing - now uses database profile lookup.
 */
export const createAuthUser = async (
  supabase: SupabaseClient<Database>,
  session: Session | null
): Promise<AuthUser | null> => {
  if (!session?.user) {
    logSecurityEvent(supabase, 'auth.session_missing', { message: 'No active session provided to createAuthUser.' });
    return null;
  }

  const sessionUser = session.user;

  try {
    // Use secure metadata-based approach instead of database queries
    // This avoids potential database schema issues and is more secure
    const metadataRole = sessionUser.app_metadata?.role || sessionUser.user_metadata?.role;
    const metadataTenantId = sessionUser.app_metadata?.tenant_id || sessionUser.user_metadata?.tenant_id;

    // Use secure defaults
    const role: UserRole = (metadataRole && isValidUserRole(metadataRole))
      ? metadataRole as UserRole
      : UserRole.Client;

    const tenantId: string | null = metadataTenantId || sessionUser.id; // Fallback to user ID
    const email: string = sessionUser.email || '';

    // Construct the AuthUser object using verified session data
    const authUser: AuthUser = {
      id: sessionUser.id,
      email: email,
      role: role,
      tenantId: tenantId,
      metadata: sessionUser.user_metadata ?? {},
    };

    logSecurityEvent(supabase, 'auth.user_created_secure', {
      message: 'AuthUser created securely from session in route-helpers',
      userId: authUser.id,
      role: authUser.role,
      tenantId: authUser.tenantId
    });

    return authUser;
  } catch (error: any) {
    logSecurityEvent(supabase, 'auth.session_processing_error', {
      message: 'Error processing session in createAuthUser (route-helpers)',
      error: error.message,
      userId: sessionUser.id
    });
    return null;
  }
};

/**
 * Type for a route handler function with authentication and typed database access
 */
export type AuthRouteHandler = (
  req: NextRequest,
  user: AuthUser,
  db: TypedDatabaseClient,
  context: Record<string, unknown>
) => Promise<Response>;

/**
 * Wraps a route handler with authentication and typed database access
 *
 * @param handler The route handler function
 * @returns A Next.js API route handler
 */
export function withTypedDb(handler: AuthRouteHandler) {
  return async (
    req: NextRequest,
    context: { params: Record<string, string> }
  ): Promise<Response> => {
    try {
      // Create a Supabase client for the API route
      // Use the ReadonlyRequestCookies directly
      const cookieStore = cookies();

      // Create a synchronous cookie store for Supabase
      const cookieObject = {
        get(name: string) {
          // @ts-expect-error - We know this is a ReadonlyRequestCookies
          const cookie = cookieStore.get?.(name);
          return cookie?.value || '';
        },
        set(name: string, value: string, options: any) {
          try {
            // @ts-expect-error - We know this is a ReadonlyRequestCookies
            cookieStore.set?.(name, value, options);
          } catch (error) {
            console.error('Error setting cookie:', error);
          }
        },
        remove(name: string, options: any) {
          try {
            // @ts-expect-error - We know this is a ReadonlyRequestCookies
            cookieStore.delete?.(name);
          } catch (error) {
            console.error('Error deleting cookie:', error);
          }
        }
      };

      const supabase = createServerClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          cookies: cookieObject
        }
      );

      // Get the current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        console.error('Authentication error:', userError);
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Get the user's tenant ID from the JWT claims
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();

      if (sessionError || !session) {
        console.error('Session error:', sessionError);
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Call createAuthUser with the supabase client
      const authUser = await createAuthUser(supabase, session);

      if (!authUser) {
        console.error('Failed to create AuthUser');
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Create a typed database client
      const db = createTypedDatabaseClient(supabase);

      // Call the handler with the authenticated user and typed database client
      return await handler(req, authUser, db, context);
    } catch (error) {
      console.error('Error in route handler:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}
