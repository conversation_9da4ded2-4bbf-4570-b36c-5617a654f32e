/**
 * API Helpers for Supabase
 * This file provides utilities for working with Supabase in API routes
 */

import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from './database.types';
import { SupabaseClient } from '@supabase/supabase-js';
import { AuthUser, UserRole, isValidUserRole } from '../types/auth';
import { enhanceClientWithSchemas } from './schema-client';
import { Session } from '@supabase/supabase-js';
// SECURITY: Removed parseJwtPayload import - using secure server-side validation instead
import { logSecurityEvent } from '../security/forensics';

/**
 * Type for a route handler function with authentication and typed database access
 */
export type AuthRouteHandler = (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database>,
  context: Record<string, unknown>
) => Promise<Response>;

/**
 * Creates an AuthUser object from a Supabase session using secure server-side validation
 * SECURITY: Removed client-side JWT parsing - now uses database profile lookup
 *
 * @param supabase The Supabase client
 * @param session The Supabase session
 * @returns Promise<AuthUser | null> The AuthUser object or null if session is invalid
 */
export const createAuthUserFromSession = async (
  supabase: SupabaseClient<Database>,
  session: Session | null
): Promise<AuthUser | null> => {
  if (!session?.user) {
    logSecurityEvent(supabase, 'auth.session_missing', { message: 'No active session found in createAuthUserFromSession.'});
    return null;
  }

  const userFromSession = session.user;

  try {
    // Get user profile from database for role and tenant information
    // Use secure metadata-based approach instead of database queries
    // This avoids potential database schema issues and is more secure
    const metadataRole = userFromSession.app_metadata?.role || userFromSession.user_metadata?.role;
    const metadataTenantId = userFromSession.app_metadata?.tenant_id || userFromSession.user_metadata?.tenant_id;

    // Use secure defaults
    const role: UserRole = (metadataRole && isValidUserRole(metadataRole))
      ? metadataRole as UserRole
      : UserRole.Client;

    const tenantId: string | null = metadataTenantId || userFromSession.id; // Fallback to user ID
    const email: string = userFromSession.email || '';

    // Construct the AuthUser object using verified session data
    const authUser: AuthUser = {
      id: userFromSession.id,
      email: email,
      role: role,
      tenantId: tenantId,
      metadata: userFromSession.user_metadata ?? {},
    };

    logSecurityEvent(supabase, 'auth.user_created_secure', {
      message: 'AuthUser created securely from session in api-helpers',
      userId: authUser.id,
      role: authUser.role,
      tenantId: authUser.tenantId
    });

    return authUser;
  } catch (error: any) {
    logSecurityEvent(supabase, 'auth.session_processing_error', {
      message: 'Error processing session in createAuthUserFromSession',
      error: error.message,
      userId: userFromSession.id
    });
    return null;
  }
};

/**
 * Wraps a route handler with authentication and typed database access
 *
 * @param handler The route handler function
 * @returns A Next.js API route handler
 */
export function withAuth(handler: AuthRouteHandler) {
  return async (
    req: NextRequest,
    context: { params: Record<string, string> }
  ): Promise<Response> => {
    try {
      // Create a Supabase client for the API route
      // Use the ReadonlyRequestCookies directly
      const cookieStore = cookies();

      // Create a synchronous cookie store for Supabase
      const cookieObject = {
        get(name: string) {
          // @ts-expect-error - We know this is a ReadonlyRequestCookies
          const cookie = cookieStore.get?.(name);
          return cookie?.value || '';
        },
        set(name: string, value: string, options: any) {
          try {
            // @ts-expect-error - We know this is a ReadonlyRequestCookies
            cookieStore.set?.(name, value, options);
          } catch (error) {
            console.error('Error setting cookie:', error);
          }
        },
        remove(name: string, options: any) {
          try {
            // @ts-expect-error - We know this is a ReadonlyRequestCookies
            cookieStore.delete?.(name);
          } catch (error) {
            console.error('Error deleting cookie:', error);
          }
        }
      };

      const supabase = createServerClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          cookies: cookieObject
        }
      );

      // Get the current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        console.error('Authentication error:', userError);
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Get the user's tenant ID from the JWT claims
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();

      if (sessionError || !session) {
        console.error('Session error:', sessionError);
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Call createAuthUserFromSession with the supabase client
      const authUser = await createAuthUserFromSession(supabase, session);

      if (!authUser) {
        console.error('Failed to create AuthUser from session');
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Enhance the Supabase client with schema support
      const enhancedClient = enhanceClientWithSchemas(supabase);

      // Call the handler with the authenticated user and enhanced client
      return await handler(req, authUser, enhancedClient, context);
    } catch (error) {
      console.error('Error in route handler:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}

/**
 * Creates a Supabase client with service role for admin operations
 *
 * @returns A Supabase client with service role
 */
export function createServiceClient() {
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_KEY!,
    {
      cookies: {
        get: (_name: string) => undefined,
        set: (_name: string, _value: string, _options: any) => {},
        remove: (_name: string, _options: any) => {}
      }
    }
  );
}

/**
 * Wraps a route handler with service role access
 *
 * @param handler The route handler function
 * @returns A Next.js API route handler
 */
export function withServiceRole(
  handler: (
    req: NextRequest,
    supabase: SupabaseClient<Database>,
    context: Record<string, unknown>
  ) => Promise<Response>
) {
  return async (
    req: NextRequest,
    context: { params: Record<string, string> }
  ): Promise<Response> => {
    try {
      // Create a service client
      const supabase = createServiceClient();

      // Enhance the Supabase client with schema support
      const enhancedClient = enhanceClientWithSchemas(supabase);

      // Call the handler with the service client
      return await handler(req, enhancedClient, context);
    } catch (error) {
      console.error('Error in service role handler:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}
