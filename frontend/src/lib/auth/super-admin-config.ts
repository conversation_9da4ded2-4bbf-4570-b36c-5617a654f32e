/**
 * Super Admin Configuration
 * 
 * Secure configuration for super admin access that supports both environment-based
 * and database-driven super admin management. This replaces hardcoded email lists
 * with a more secure and flexible approach.
 */

import { env } from '@/config/env';

/**
 * Super Admin Configuration Interface
 */
export interface SuperAdminConfig {
  emails: string[];
  isDatabaseDriven: boolean;
  isEnvironmentConfigured: boolean;
}

/**
 * Get super admin configuration from environment variables
 * This is the primary source for super admin emails in production
 */
function getEnvironmentSuperAdminEmails(): string[] {
  try {
    return env.superAdminEmails;
  } catch (error) {
    console.error('Failed to load super admin emails from environment:', error);
    return [];
  }
}

/**
 * Fallback super admin emails for development/testing
 * These should NOT be used in production
 */
const DEVELOPMENT_FALLBACK_EMAILS = [
  '<EMAIL>',
  '<EMAIL>'
] as const;

/**
 * Get the current super admin configuration
 * Prioritizes environment variables over fallback values
 */
export function getSuperAdminConfig(): SuperAdminConfig {
  const environmentEmails = getEnvironmentSuperAdminEmails();
  const isEnvironmentConfigured = environmentEmails.length > 0;
  
  // In production, require environment configuration
  if (process.env.NODE_ENV === 'production' && !isEnvironmentConfigured) {
    throw new Error(
      'SUPER_ADMIN_EMAILS environment variable must be configured in production. ' +
      'Set SUPER_ADMIN_EMAILS="<EMAIL>,<EMAIL>" or use JSON format.'
    );
  }
  
  // Use environment emails if available, otherwise fallback for development
  const emails = isEnvironmentConfigured 
    ? environmentEmails 
    : [...DEVELOPMENT_FALLBACK_EMAILS];
  
  return {
    emails,
    isDatabaseDriven: false, // Will be implemented in Task 2.1.2
    isEnvironmentConfigured
  };
}

/**
 * Check if an email is configured as a super admin email
 * This replaces the hardcoded SUPER_ADMIN_EMAILS.includes() checks
 */
export function isSuperAdminEmail(email: string | null | undefined): boolean {
  if (!email) return false;
  
  try {
    const config = getSuperAdminConfig();
    return config.emails.includes(email);
  } catch (error) {
    console.error('Failed to check super admin email:', error);
    return false;
  }
}

/**
 * Get all configured super admin emails
 * Use this instead of importing SUPER_ADMIN_EMAILS directly
 */
export function getSuperAdminEmails(): string[] {
  try {
    const config = getSuperAdminConfig();
    return [...config.emails]; // Return a copy to prevent mutation
  } catch (error) {
    console.error('Failed to get super admin emails:', error);
    return [];
  }
}

/**
 * Validate super admin configuration
 * This should be called during application startup
 */
export function validateSuperAdminConfig(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  try {
    const config = getSuperAdminConfig();
    
    // Check if any emails are configured
    if (config.emails.length === 0) {
      errors.push('No super admin emails configured');
    }
    
    // Validate email formats
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const invalidEmails = config.emails.filter(email => !emailRegex.test(email));
    if (invalidEmails.length > 0) {
      errors.push(`Invalid email format(s): ${invalidEmails.join(', ')}`);
    }
    
    // Check for development fallback usage in production
    if (process.env.NODE_ENV === 'production' && !config.isEnvironmentConfigured) {
      errors.push('Using development fallback emails in production');
    }
    
    // Warn about development fallback usage
    if (!config.isEnvironmentConfigured) {
      warnings.push('Using development fallback super admin emails. Set SUPER_ADMIN_EMAILS environment variable for production.');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  } catch (error) {
    return {
      isValid: false,
      errors: [`Configuration validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
      warnings
    };
  }
}

/**
 * Log super admin configuration status (for debugging)
 * This should be called during application startup
 */
export function logSuperAdminConfigStatus(): void {
  try {
    const validation = validateSuperAdminConfig();
    const config = getSuperAdminConfig();
    
    console.log('Super Admin Configuration Status:');
    console.log(`  Environment Configured: ${config.isEnvironmentConfigured}`);
    console.log(`  Total Emails: ${config.emails.length}`);
    console.log(`  Database Driven: ${config.isDatabaseDriven}`);
    console.log(`  Valid: ${validation.isValid}`);
    
    if (validation.warnings.length > 0) {
      console.warn('Super Admin Configuration Warnings:');
      validation.warnings.forEach(warning => console.warn(`  - ${warning}`));
    }
    
    if (validation.errors.length > 0) {
      console.error('Super Admin Configuration Errors:');
      validation.errors.forEach(error => console.error(`  - ${error}`));
    }
    
    // In development, show configured emails (masked)
    if (process.env.NODE_ENV === 'development') {
      console.log('Configured Super Admin Emails (masked):');
      config.emails.forEach(email => {
        const [local, domain] = email.split('@');
        const maskedLocal = local.length > 2 ? `${local[0]}***${local[local.length - 1]}` : '***';
        console.log(`  - ${maskedLocal}@${domain}`);
      });
    }
  } catch (error) {
    console.error('Failed to log super admin configuration status:', error);
  }
}

// Legacy compatibility exports (to be removed after migration)
// These are temporary to maintain compatibility during the migration
export const SUPER_ADMIN_EMAILS = getSuperAdminEmails();
export type SuperAdminEmail = string;
