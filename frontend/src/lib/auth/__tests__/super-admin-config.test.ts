/**
 * Super Admin Configuration Tests
 * 
 * Tests for the secure super admin configuration system that replaces
 * hardcoded email lists with environment-based configuration.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
  getSuperAdminConfig,
  isSuperAdminEmail,
  getSuperAdminEmails,
  validateSuperAdminConfig,
  logSuperAdminConfigStatus
} from '../super-admin-config';

// Mock the environment configuration
vi.mock('@/config/env', () => ({
  env: {
    superAdminEmails: ['<EMAIL>', '<EMAIL>']
  }
}));

describe('Super Admin Configuration', () => {
  const originalEnv = process.env.NODE_ENV;
  
  beforeEach(() => {
    // Reset console mocks
    vi.clearAllMocks();
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });
  
  afterEach(() => {
    process.env.NODE_ENV = originalEnv;
  });

  describe('getSuperAdminConfig', () => {
    it('should return environment-configured emails', () => {
      const config = getSuperAdminConfig();
      
      expect(config.emails).toEqual(['<EMAIL>', '<EMAIL>']);
      expect(config.isEnvironmentConfigured).toBe(true);
      expect(config.isDatabaseDriven).toBe(false);
    });

    it('should throw error in production without environment configuration', () => {
      process.env.NODE_ENV = 'production';
      
      // Mock empty environment configuration
      vi.doMock('@/config/env', () => ({
        env: {
          superAdminEmails: []
        }
      }));
      
      expect(() => getSuperAdminConfig()).toThrow(
        'SUPER_ADMIN_EMAILS environment variable must be configured in production'
      );
    });
  });

  describe('isSuperAdminEmail', () => {
    it('should return true for configured super admin emails', () => {
      expect(isSuperAdminEmail('<EMAIL>')).toBe(true);
      expect(isSuperAdminEmail('<EMAIL>')).toBe(true);
    });

    it('should return false for non-configured emails', () => {
      expect(isSuperAdminEmail('<EMAIL>')).toBe(false);
      expect(isSuperAdminEmail('<EMAIL>')).toBe(false);
    });

    it('should return false for null or undefined emails', () => {
      expect(isSuperAdminEmail(null)).toBe(false);
      expect(isSuperAdminEmail(undefined)).toBe(false);
      expect(isSuperAdminEmail('')).toBe(false);
    });

    it('should handle configuration errors gracefully', () => {
      // Mock configuration error
      vi.doMock('@/config/env', () => {
        throw new Error('Configuration error');
      });
      
      expect(isSuperAdminEmail('<EMAIL>')).toBe(false);
      expect(console.error).toHaveBeenCalledWith(
        'Failed to check super admin email:',
        expect.any(Error)
      );
    });
  });

  describe('getSuperAdminEmails', () => {
    it('should return a copy of configured emails', () => {
      const emails = getSuperAdminEmails();
      
      expect(emails).toEqual(['<EMAIL>', '<EMAIL>']);
      
      // Verify it returns a copy (mutation doesn't affect original)
      emails.push('<EMAIL>');
      const emails2 = getSuperAdminEmails();
      expect(emails2).toEqual(['<EMAIL>', '<EMAIL>']);
    });

    it('should return empty array on configuration error', () => {
      // Mock configuration error
      vi.doMock('@/config/env', () => {
        throw new Error('Configuration error');
      });
      
      const emails = getSuperAdminEmails();
      expect(emails).toEqual([]);
      expect(console.error).toHaveBeenCalledWith(
        'Failed to get super admin emails:',
        expect.any(Error)
      );
    });
  });

  describe('validateSuperAdminConfig', () => {
    it('should validate correct configuration', () => {
      const validation = validateSuperAdminConfig();
      
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toEqual([]);
      expect(validation.warnings).toEqual([]);
    });

    it('should detect invalid email formats', () => {
      // Mock invalid email configuration
      vi.doMock('@/config/env', () => ({
        env: {
          superAdminEmails: ['<EMAIL>', 'invalid-email', '<EMAIL>']
        }
      }));
      
      const validation = validateSuperAdminConfig();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Invalid email format(s): invalid-email');
    });

    it('should detect empty configuration', () => {
      // Mock empty configuration
      vi.doMock('@/config/env', () => ({
        env: {
          superAdminEmails: []
        }
      }));
      
      const validation = validateSuperAdminConfig();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('No super admin emails configured');
    });

    it('should handle validation errors gracefully', () => {
      // Mock configuration error
      vi.doMock('@/config/env', () => {
        throw new Error('Configuration error');
      });
      
      const validation = validateSuperAdminConfig();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors[0]).toContain('Configuration validation failed');
    });
  });

  describe('logSuperAdminConfigStatus', () => {
    it('should log configuration status without errors', () => {
      logSuperAdminConfigStatus();
      
      expect(console.log).toHaveBeenCalledWith('Super Admin Configuration Status:');
      expect(console.log).toHaveBeenCalledWith('  Environment Configured: true');
      expect(console.log).toHaveBeenCalledWith('  Total Emails: 2');
      expect(console.log).toHaveBeenCalledWith('  Database Driven: false');
      expect(console.log).toHaveBeenCalledWith('  Valid: true');
    });

    it('should handle logging errors gracefully', () => {
      // Mock configuration error
      vi.doMock('@/config/env', () => {
        throw new Error('Configuration error');
      });
      
      logSuperAdminConfigStatus();
      
      expect(console.error).toHaveBeenCalledWith(
        'Failed to log super admin configuration status:',
        expect.any(Error)
      );
    });
  });
});
