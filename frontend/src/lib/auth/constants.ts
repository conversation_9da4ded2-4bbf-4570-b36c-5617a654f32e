/**
 * Authentication Constants
 * Centralized constants for authentication and authorization
 */

// SECURITY: Import secure super admin configuration instead of hardcoded emails
import {
  getSuperAdminEmails,
  isSuperAdminEmail as checkIsSuperAdminEmail,
  type SuperAdminEmail as SecureSuperAdminEmail
} from './super-admin-config';

/**
 * Super Admin Email List
 * DEPRECATED: Use getSuperAdminEmails() from super-admin-config instead
 * This is maintained for backward compatibility during migration
 */
export const SUPER_ADMIN_EMAILS = getSuperAdminEmails();

/**
 * Type for super admin emails
 * DEPRECATED: Use SuperAdminEmail from super-admin-config instead
 */
export type SuperAdminEmail = SecureSuperAdminEmail;

/**
 * Check if an email is a super admin email
 * DEPRECATED: Use isSuperAdminEmail from super-admin-config instead
 */
export function isSuperAdminEmail(email: string | null | undefined): boolean {
  return checkIsSuperAdminEmail(email);
}
