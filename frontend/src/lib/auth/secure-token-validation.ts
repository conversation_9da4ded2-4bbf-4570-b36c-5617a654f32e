/**
 * Secure Token Validation Client
 * Replaces client-side JWT parsing with secure server-side validation
 */

import { SecureTokenValidationResponse } from '@/app/api/auth/validate-token/route';

export interface SecureUserClaims {
  id: string;
  email: string;
  role: string;
  tenant_id: string;
  is_super_admin?: boolean;
  expires_at?: string;
}

/**
 * Securely validates a JWT token using server-side validation
 * Replaces the unsafe parseJwtPayload function
 * 
 * @param token The JWT token to validate
 * @returns Promise<SecureUserClaims | null> Validated user claims or null if invalid
 */
export async function validateTokenSecurely(token: string): Promise<SecureUserClaims | null> {
  if (!token) {
    return null;
  }

  try {
    const response = await fetch('/api/auth/validate-token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ token }),
    });

    if (!response.ok) {
      console.warn('Token validation failed:', response.status);
      return null;
    }

    const result: SecureTokenValidationResponse = await response.json();

    if (!result.valid || !result.user) {
      console.warn('Token validation returned invalid result');
      return null;
    }

    return {
      id: result.user.id,
      email: result.user.email,
      role: result.user.role,
      tenant_id: result.user.tenant_id,
      is_super_admin: result.user.is_super_admin,
      expires_at: result.expires_at,
    };

  } catch (error) {
    console.error('Token validation request failed:', error);
    return null;
  }
}

/**
 * Gets validated user claims from the current session
 * Secure replacement for getJwtPayload function
 * 
 * @returns Promise<SecureUserClaims | null> Current user claims or null
 */
export async function getCurrentUserClaims(): Promise<SecureUserClaims | null> {
  // Get token from session storage or cookies
  const token = getTokenFromStorage();
  
  if (!token) {
    return null;
  }

  return validateTokenSecurely(token);
}

/**
 * Helper to get token from storage (cookies or session storage)
 * This is a safe way to retrieve tokens without parsing them
 */
function getTokenFromStorage(): string | null {
  // Try to get from cookies first (more secure)
  if (typeof document !== 'undefined') {
    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === 'sb-access-token') {
        return decodeURIComponent(value);
      }
    }
  }

  // Fallback to session storage (less secure but sometimes necessary)
  if (typeof window !== 'undefined' && window.sessionStorage) {
    return window.sessionStorage.getItem('supabase.auth.token');
  }

  return null;
}

/**
 * Cache for validated tokens to reduce server requests
 * Note: This is a simple in-memory cache. In production, consider using
 * a more sophisticated caching strategy with proper invalidation.
 */
const tokenCache = new Map<string, { claims: SecureUserClaims; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Validates token with caching to reduce server load
 * 
 * @param token The JWT token to validate
 * @returns Promise<SecureUserClaims | null> Cached or freshly validated claims
 */
export async function validateTokenWithCache(token: string): Promise<SecureUserClaims | null> {
  if (!token) {
    return null;
  }

  // Check cache first
  const cached = tokenCache.get(token);
  if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION) {
    return cached.claims;
  }

  // Validate with server
  const claims = await validateTokenSecurely(token);
  
  if (claims) {
    // Cache the result
    tokenCache.set(token, {
      claims,
      timestamp: Date.now()
    });
  }

  return claims;
}

/**
 * Clears the token cache (useful for logout or token refresh)
 */
export function clearTokenCache(): void {
  tokenCache.clear();
}
