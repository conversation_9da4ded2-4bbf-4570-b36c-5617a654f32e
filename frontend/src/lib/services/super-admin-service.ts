/**
 * Super Admin Management Service
 * 
 * Service for managing database-driven super admin configuration.
 * Provides CRUD operations and validation for super admin users.
 */

import { createClient } from '@/lib/supabase/server';
import { 
  SuperAdminRecord,
  addSuperAdminEmail,
  removeSuperAdminEmail,
  getAllSuperAdminRecords,
  getSuperAdminConfigAsync,
  isSuperAdminEmailAsync
} from '@/lib/auth/super-admin-config';

/**
 * Super Admin Management Interface
 */
export interface SuperAdminManagementResult {
  success: boolean;
  error?: string;
  data?: any;
}

/**
 * Super Admin Service Class
 */
export class SuperAdminService {
  /**
   * Get all super admin records with enhanced information
   */
  static async getAllSuperAdmins(): Promise<SuperAdminManagementResult> {
    try {
      const records = await getAllSuperAdminRecords();
      const config = await getSuperAdminConfigAsync();
      
      // Enhance records with source information
      const enhancedRecords = records.map(record => ({
        ...record,
        source: config.environmentEmails.includes(record.email) 
          ? (config.databaseEmails.includes(record.email) ? 'hybrid' : 'environment')
          : 'database',
        canBeRemoved: !config.environmentEmails.includes(record.email) // Can't remove env-configured admins
      }));
      
      return {
        success: true,
        data: {
          records: enhancedRecords,
          config: {
            totalCount: config.emails.length,
            environmentCount: config.environmentEmails.length,
            databaseCount: config.databaseEmails.length,
            source: config.source
          }
        }
      };
    } catch (error) {
      console.error('Failed to get all super admins:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Add a new super admin
   */
  static async addSuperAdmin(
    email: string,
    createdBy: string,
    notes?: string
  ): Promise<SuperAdminManagementResult> {
    try {
      // Validate that the creator is a super admin
      const isCreatorSuperAdmin = await isSuperAdminEmailAsync(createdBy);
      if (!isCreatorSuperAdmin) {
        return {
          success: false,
          error: 'Only super admins can add new super admins'
        };
      }
      
      const result = await addSuperAdminEmail(email, createdBy, notes);
      return result;
    } catch (error) {
      console.error('Failed to add super admin:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Remove (deactivate) a super admin
   */
  static async removeSuperAdmin(
    email: string,
    updatedBy: string
  ): Promise<SuperAdminManagementResult> {
    try {
      // Validate that the updater is a super admin
      const isUpdaterSuperAdmin = await isSuperAdminEmailAsync(updatedBy);
      if (!isUpdaterSuperAdmin) {
        return {
          success: false,
          error: 'Only super admins can remove super admins'
        };
      }
      
      // Check if this is an environment-configured super admin
      const config = await getSuperAdminConfigAsync();
      if (config.environmentEmails.includes(email)) {
        return {
          success: false,
          error: 'Cannot remove environment-configured super admin. Remove from SUPER_ADMIN_EMAILS environment variable instead.'
        };
      }
      
      // Prevent removing the last super admin
      const activeDatabaseAdmins = config.databaseEmails.length;
      const environmentAdmins = config.environmentEmails.length;
      
      if (activeDatabaseAdmins <= 1 && environmentAdmins === 0) {
        return {
          success: false,
          error: 'Cannot remove the last super admin. At least one super admin must remain.'
        };
      }
      
      const result = await removeSuperAdminEmail(email, updatedBy);
      return result;
    } catch (error) {
      console.error('Failed to remove super admin:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Update super admin notes
   */
  static async updateSuperAdminNotes(
    email: string,
    notes: string,
    updatedBy: string
  ): Promise<SuperAdminManagementResult> {
    try {
      // Validate that the updater is a super admin
      const isUpdaterSuperAdmin = await isSuperAdminEmailAsync(updatedBy);
      if (!isUpdaterSuperAdmin) {
        return {
          success: false,
          error: 'Only super admins can update super admin records'
        };
      }
      
      const supabase = createClient();
      
      const { error } = await supabase
        .from('super_admin_config')
        .update({
          notes,
          updated_by: updatedBy,
          updated_at: new Date().toISOString()
        })
        .eq('email', email);
      
      if (error) {
        console.error('Failed to update super admin notes:', error);
        return { success: false, error: error.message };
      }
      
      return { success: true };
    } catch (error) {
      console.error('Error updating super admin notes:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Reactivate a deactivated super admin
   */
  static async reactivateSuperAdmin(
    email: string,
    updatedBy: string
  ): Promise<SuperAdminManagementResult> {
    try {
      // Validate that the updater is a super admin
      const isUpdaterSuperAdmin = await isSuperAdminEmailAsync(updatedBy);
      if (!isUpdaterSuperAdmin) {
        return {
          success: false,
          error: 'Only super admins can reactivate super admins'
        };
      }
      
      const supabase = createClient();
      
      const { error } = await supabase
        .from('super_admin_config')
        .update({
          is_active: true,
          updated_by: updatedBy,
          updated_at: new Date().toISOString()
        })
        .eq('email', email);
      
      if (error) {
        console.error('Failed to reactivate super admin:', error);
        return { success: false, error: error.message };
      }
      
      return { success: true };
    } catch (error) {
      console.error('Error reactivating super admin:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Get super admin audit log
   */
  static async getSuperAdminAuditLog(): Promise<SuperAdminManagementResult> {
    try {
      const supabase = createClient();
      
      const { data, error } = await supabase
        .from('log')
        .select('*')
        .eq('application_name', 'super_admin_config')
        .order('log_time', { ascending: false })
        .limit(100);
      
      if (error) {
        console.error('Failed to get super admin audit log:', error);
        return { success: false, error: error.message };
      }
      
      return {
        success: true,
        data: data || []
      };
    } catch (error) {
      console.error('Error getting super admin audit log:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Validate super admin configuration health
   */
  static async validateConfiguration(): Promise<SuperAdminManagementResult> {
    try {
      const config = await getSuperAdminConfigAsync();
      
      const issues: string[] = [];
      const warnings: string[] = [];
      
      // Check if any super admins are configured
      if (config.emails.length === 0) {
        issues.push('No super admins configured');
      }
      
      // Check for environment vs database consistency
      if (config.source === 'hybrid') {
        warnings.push('Super admins configured in both environment and database. Environment takes precedence.');
      }
      
      // Check for inactive database records
      const allRecords = await getAllSuperAdminRecords();
      const inactiveCount = allRecords.filter(r => !r.is_active).length;
      if (inactiveCount > 0) {
        warnings.push(`${inactiveCount} inactive super admin record(s) in database`);
      }
      
      return {
        success: true,
        data: {
          config,
          issues,
          warnings,
          isHealthy: issues.length === 0
        }
      };
    } catch (error) {
      console.error('Error validating super admin configuration:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}

export default SuperAdminService;
