/**
 * Environment Configuration
 * 
 * Centralized configuration for environment variables used throughout the application.
 * Provides type-safe access to environment variables with validation.
 */

export interface EnvironmentConfig {
  // MCP Rules Engine Configuration
  mcpRulesBase: string;
  mcpRulesBaseStaging?: string;
  featureMcpRulesEngine: boolean;

  // Supabase Configuration
  supabaseUrl: string;
  supabaseAnonKey: string;

  // Google Cloud Configuration
  googleCloudProject: string;
  mcpProject: string;
  tenantProject: string;

  // Feature Flags
  useSupabaseAuth: boolean;

  // Security Configuration
  superAdminEmails: string[];
}

/**
 * Validates and returns the environment configuration
 * Throws an error if required environment variables are missing
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  const mcpRulesBase = process.env.MCP_RULES_BASE;
  
  if (!mcpRulesBase) {
    throw new Error('MCP_RULES_BASE environment variable is required but not set');
  }
  
  // Validate that MCP_RULES_BASE is using the correct API Gateway format
  if (!mcpRulesBase.includes('uc.gateway.dev')) {
    throw new Error(`MCP_RULES_BASE must use API Gateway host (uc.gateway.dev), got: ${mcpRulesBase}`);
  }
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  if (!supabaseUrl) {
    throw new Error('NEXT_PUBLIC_SUPABASE_URL environment variable is required but not set');
  }
  
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  if (!supabaseAnonKey) {
    throw new Error('NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable is required but not set');
  }
  
  // Parse super admin emails from environment variable
  const superAdminEmailsEnv = process.env.SUPER_ADMIN_EMAILS;
  let superAdminEmails: string[] = [];

  if (superAdminEmailsEnv) {
    try {
      // Support both comma-separated and JSON array formats
      if (superAdminEmailsEnv.startsWith('[')) {
        superAdminEmails = JSON.parse(superAdminEmailsEnv);
      } else {
        superAdminEmails = superAdminEmailsEnv.split(',').map(email => email.trim()).filter(Boolean);
      }

      // Validate email formats
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const invalidEmails = superAdminEmails.filter(email => !emailRegex.test(email));
      if (invalidEmails.length > 0) {
        throw new Error(`Invalid super admin email format(s): ${invalidEmails.join(', ')}`);
      }
    } catch (error) {
      throw new Error(`Failed to parse SUPER_ADMIN_EMAILS environment variable: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  } else {
    console.warn('SUPER_ADMIN_EMAILS environment variable not set. Super admin functionality will be limited to database-driven configuration.');
  }

  return {
    // MCP Rules Engine Configuration
    mcpRulesBase,
    mcpRulesBaseStaging: process.env.MCP_RULES_BASE_STG,
    featureMcpRulesEngine: process.env.FEATURE_MCP_RULES_ENGINE === 'true',

    // Supabase Configuration
    supabaseUrl,
    supabaseAnonKey,

    // Google Cloud Configuration
    googleCloudProject: process.env.GOOGLE_CLOUD_PROJECT || 'new-texas-laws',
    mcpProject: process.env.MCP_PROJECT || 'texas-laws-personalinjury',
    tenantProject: process.env.TENANT_PROJECT || 'new-texas-laws',

    // Feature Flags
    useSupabaseAuth: process.env.NEXT_PUBLIC_USE_SUPABASE_AUTH === 'true',

    // Security Configuration
    superAdminEmails,
  };
}

/**
 * Environment configuration instance
 * Use this throughout the application for consistent environment variable access
 */
export const env = getEnvironmentConfig();

/**
 * Development helper to check if we're in development mode
 */
export const isDevelopment = process.env.NODE_ENV === 'development';

/**
 * Production helper to check if we're in production mode
 */
export const isProduction = process.env.NODE_ENV === 'production';

/**
 * Staging helper to check if we're in staging mode
 */
export const isStaging = process.env.VERCEL_ENV === 'preview' || process.env.NODE_ENV === 'development';

/**
 * Get the appropriate MCP Rules Base URL based on environment
 */
export function getMcpRulesBaseUrl(): string {
  const config = getEnvironmentConfig();
  
  if (isStaging && config.mcpRulesBaseStaging) {
    return config.mcpRulesBaseStaging;
  }
  
  return config.mcpRulesBase;
}
