-- Migration: Create Super Admin Configuration Table
-- Description: Database-driven super admin configuration system for dynamic management
-- Date: 2025-01-07
-- Task: Phase 2 Task 2.1.2 - Implement database-driven super admin configuration

-- Create the super_admin_config table in the security schema
CREATE TABLE IF NOT EXISTS security.super_admin_config (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT NOT NULL UNIQUE,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    created_by TEXT NULL, -- Email of the user who created this record
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_by TEXT NULL, -- Email of the user who last updated this record
    notes TEXT NULL, -- Optional notes about this super admin
    
    -- Constraints
    CONSTRAINT super_admin_config_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT super_admin_config_email_not_empty CHECK (length(trim(email)) > 0)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_super_admin_config_email ON security.super_admin_config(email);
CREATE INDEX IF NOT EXISTS idx_super_admin_config_active ON security.super_admin_config(is_active);
CREATE INDEX IF NOT EXISTS idx_super_admin_config_created_at ON security.super_admin_config(created_at);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION security.update_super_admin_config_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_super_admin_config_updated_at
    BEFORE UPDATE ON security.super_admin_config
    FOR EACH ROW
    EXECUTE FUNCTION security.update_super_admin_config_updated_at();

-- Row Level Security (RLS) Policies
ALTER TABLE security.super_admin_config ENABLE ROW LEVEL SECURITY;

-- Policy: Only super admins can read super admin configuration
CREATE POLICY "super_admin_config_select_policy" ON security.super_admin_config
    FOR SELECT
    USING (
        -- Allow if user is authenticated and is a super admin
        auth.uid() IS NOT NULL AND (
            -- Check if user has super admin claim in JWT
            (auth.jwt() ->> 'is_super_admin')::boolean = true OR
            -- Check if user email is in the super admin configuration (bootstrap case)
            auth.jwt() ->> 'email' IN (
                SELECT email FROM security.super_admin_config WHERE is_active = true
            )
        )
    );

-- Policy: Only super admins can insert super admin configuration
CREATE POLICY "super_admin_config_insert_policy" ON security.super_admin_config
    FOR INSERT
    WITH CHECK (
        auth.uid() IS NOT NULL AND (
            (auth.jwt() ->> 'is_super_admin')::boolean = true OR
            auth.jwt() ->> 'email' IN (
                SELECT email FROM security.super_admin_config WHERE is_active = true
            )
        )
    );

-- Policy: Only super admins can update super admin configuration
CREATE POLICY "super_admin_config_update_policy" ON security.super_admin_config
    FOR UPDATE
    USING (
        auth.uid() IS NOT NULL AND (
            (auth.jwt() ->> 'is_super_admin')::boolean = true OR
            auth.jwt() ->> 'email' IN (
                SELECT email FROM security.super_admin_config WHERE is_active = true
            )
        )
    );

-- Policy: Prevent deletion (use is_active = false instead)
CREATE POLICY "super_admin_config_delete_policy" ON security.super_admin_config
    FOR DELETE
    USING (false); -- No deletions allowed, use deactivation instead

-- Create audit log function for super admin changes
CREATE OR REPLACE FUNCTION security.log_super_admin_config_changes()
RETURNS TRIGGER AS $$
BEGIN
    -- Log the change to audit.log table
    INSERT INTO audit.log (
        log_time,
        user_name,
        database_name,
        application_name,
        message,
        detail
    ) VALUES (
        now(),
        COALESCE(auth.jwt() ->> 'email', 'system'),
        current_database(),
        'super_admin_config',
        CASE 
            WHEN TG_OP = 'INSERT' THEN 'Super admin added: ' || NEW.email
            WHEN TG_OP = 'UPDATE' THEN 'Super admin updated: ' || NEW.email
            WHEN TG_OP = 'DELETE' THEN 'Super admin deleted: ' || OLD.email
        END,
        CASE 
            WHEN TG_OP = 'INSERT' THEN 'Added super admin with notes: ' || COALESCE(NEW.notes, 'none')
            WHEN TG_OP = 'UPDATE' THEN 
                'Updated super admin. Active: ' || NEW.is_active::text || 
                ', Notes: ' || COALESCE(NEW.notes, 'none') ||
                ', Updated by: ' || COALESCE(NEW.updated_by, 'unknown')
            WHEN TG_OP = 'DELETE' THEN 'Deleted super admin (should not happen - use deactivation)'
        END
    );
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create audit trigger
CREATE TRIGGER trigger_super_admin_config_audit
    AFTER INSERT OR UPDATE OR DELETE ON security.super_admin_config
    FOR EACH ROW
    EXECUTE FUNCTION security.log_super_admin_config_changes();

-- Insert initial super admin records from environment (if any)
-- This is a bootstrap mechanism for the first deployment
DO $$
DECLARE
    admin_email TEXT;
    admin_emails TEXT[] := string_to_array(
        COALESCE(current_setting('app.super_admin_emails', true), ''), 
        ','
    );
BEGIN
    -- Only insert if the table is empty (bootstrap case)
    IF NOT EXISTS (SELECT 1 FROM security.super_admin_config LIMIT 1) THEN
        FOREACH admin_email IN ARRAY admin_emails
        LOOP
            admin_email := trim(admin_email);
            IF admin_email != '' AND admin_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' THEN
                INSERT INTO security.super_admin_config (email, created_by, notes)
                VALUES (
                    admin_email, 
                    'system_bootstrap',
                    'Bootstrapped from environment configuration during initial deployment'
                )
                ON CONFLICT (email) DO NOTHING;
            END IF;
        END LOOP;
    END IF;
END $$;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON security.super_admin_config TO authenticated;
GRANT USAGE ON SCHEMA security TO authenticated;

-- Comments for documentation
COMMENT ON TABLE security.super_admin_config IS 'Database-driven super admin configuration for dynamic management of platform super administrators';
COMMENT ON COLUMN security.super_admin_config.email IS 'Super admin email address (must be unique and valid format)';
COMMENT ON COLUMN security.super_admin_config.is_active IS 'Whether this super admin is currently active (use false instead of deletion for audit trail)';
COMMENT ON COLUMN security.super_admin_config.created_by IS 'Email of the user who created this super admin record';
COMMENT ON COLUMN security.super_admin_config.updated_by IS 'Email of the user who last updated this super admin record';
COMMENT ON COLUMN security.super_admin_config.notes IS 'Optional notes about this super admin (e.g., role, department, reason for access)';
