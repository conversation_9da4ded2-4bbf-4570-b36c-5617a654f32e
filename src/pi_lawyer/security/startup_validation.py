"""
Startup Security Validation Module

This module provides comprehensive security validation that runs at application startup
to ensure all critical security configurations are properly set before the application
starts serving requests.
"""

import logging
import os
import re
from typing import Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)


class SecurityValidationError(Exception):
    """Exception raised when security validation fails."""
    pass


class StartupSecurityValidator:
    """Validates security configuration at application startup."""
    
    def __init__(self):
        self.app_env = os.getenv("APP_ENV", "development")
        self.node_env = os.getenv("NODE_ENV", "development")
        self.errors: List[str] = []
        self.warnings: List[str] = []
    
    def validate_all(self) -> Tuple[bool, List[str], List[str]]:
        """
        Run all security validations.
        
        Returns:
            Tuple of (is_valid, errors, warnings)
        """
        self.errors.clear()
        self.warnings.clear()
        
        # Run all validation checks
        self._validate_jwt_configuration()
        self._validate_supabase_configuration()
        self._validate_api_secrets()
        self._validate_environment_consistency()
        self._validate_production_requirements()
        
        is_valid = len(self.errors) == 0
        return is_valid, self.errors.copy(), self.warnings.copy()
    
    def _validate_jwt_configuration(self):
        """Validate JWT configuration."""
        jwt_secret = os.getenv("SUPABASE_JWT_SECRET")
        
        if not jwt_secret:
            self.errors.append(
                f"SUPABASE_JWT_SECRET is required for {self.app_env} environment. "
                "Generate a secure secret with: openssl rand -base64 32"
            )
            return
        
        # Validate JWT secret strength
        if len(jwt_secret) < 32:
            self.errors.append(
                "SUPABASE_JWT_SECRET must be at least 32 characters long for security. "
                "Generate a longer secret with: openssl rand -base64 32"
            )
        
        # Check for common weak patterns
        weak_patterns = [
            r"^(test|dev|demo|example|placeholder|default)",
            r"^(123|abc|password|secret)",
            r"^[0-9]+$",  # Only numbers
            r"^[a-z]+$",  # Only lowercase letters
        ]
        
        for pattern in weak_patterns:
            if re.match(pattern, jwt_secret.lower()):
                self.errors.append(
                    "SUPABASE_JWT_SECRET appears to use a weak or default value. "
                    "Generate a secure secret with: openssl rand -base64 32"
                )
                break
        
        # Validate JWT algorithm
        jwt_algorithm = os.getenv("JWT_ALGORITHM", "HS256")
        allowed_algorithms = ["HS256", "HS384", "HS512", "RS256", "RS384", "RS512"]
        
        if jwt_algorithm not in allowed_algorithms:
            self.errors.append(
                f"JWT_ALGORITHM '{jwt_algorithm}' is not supported. "
                f"Use one of: {', '.join(allowed_algorithms)}"
            )
    
    def _validate_supabase_configuration(self):
        """Validate Supabase configuration."""
        supabase_url = os.getenv("SUPABASE_URL") or os.getenv("NEXT_PUBLIC_SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_KEY")
        
        if not supabase_url:
            self.errors.append("SUPABASE_URL or NEXT_PUBLIC_SUPABASE_URL is required")
        elif not supabase_url.startswith("https://"):
            self.errors.append("SUPABASE_URL must use HTTPS")
        elif "supabase.co" not in supabase_url and self.app_env == "production":
            self.warnings.append("SUPABASE_URL does not appear to be a valid Supabase URL")
        
        if not supabase_key:
            self.errors.append("SUPABASE_SERVICE_KEY is required")
        elif len(supabase_key) < 100:  # Supabase service keys are typically much longer
            self.warnings.append("SUPABASE_SERVICE_KEY appears to be too short")
    
    def _validate_api_secrets(self):
        """Validate API secrets and keys."""
        # CopilotKit endpoint secret
        cpk_secret = os.getenv("CPK_ENDPOINT_SECRET")
        if cpk_secret:
            if cpk_secret == "test-endpoint-secret-123456789":
                self.errors.append(
                    "CPK_ENDPOINT_SECRET is using the default test value. "
                    "Generate a secure secret for production use."
                )
            elif len(cpk_secret) < 16:
                self.warnings.append("CPK_ENDPOINT_SECRET should be at least 16 characters long")
        
        # Core intake secret
        core_secret = os.getenv("CORE_INTAKE_SECRET")
        if core_secret and len(core_secret) < 16:
            self.warnings.append("CORE_INTAKE_SECRET should be at least 16 characters long")
        
        # Stripe webhook secret (if using Stripe)
        stripe_webhook = os.getenv("STRIPE_WEBHOOK_SECRET")
        if stripe_webhook and not stripe_webhook.startswith("whsec_"):
            self.warnings.append("STRIPE_WEBHOOK_SECRET should start with 'whsec_'")
    
    def _validate_environment_consistency(self):
        """Validate environment variable consistency."""
        # Check for environment consistency
        if self.app_env == "production" and self.node_env != "production":
            self.warnings.append(
                f"APP_ENV is '{self.app_env}' but NODE_ENV is '{self.node_env}'. "
                "Consider setting both to 'production' for consistency."
            )
        
        # Check for development settings in production
        if self.app_env == "production":
            dev_indicators = [
                ("NEXT_PUBLIC_SUPABASE_URL", "localhost"),
                ("SUPABASE_URL", "localhost"),
                ("DB_HOST", "localhost"),
                ("REDIS_URL", "localhost"),
            ]
            
            for env_var, indicator in dev_indicators:
                value = os.getenv(env_var, "")
                if indicator in value.lower():
                    self.warnings.append(
                        f"{env_var} contains '{indicator}' which may not be appropriate for production"
                    )
    
    def _validate_production_requirements(self):
        """Validate production-specific requirements."""
        if self.app_env != "production":
            return
        
        # Required environment variables for production
        required_prod_vars = [
            "SUPABASE_JWT_SECRET",
            "SUPABASE_URL",
            "SUPABASE_SERVICE_KEY",
            "OPENAI_API_KEY",
        ]
        
        for var in required_prod_vars:
            if not os.getenv(var):
                self.errors.append(f"{var} is required for production environment")
        
        # Check for test/development values in production
        test_patterns = [
            ("SUPABASE_URL", "test-project"),
            ("SUPABASE_SERVICE_KEY", "test123456789"),
            ("OPENAI_API_KEY", "test"),
            ("PINECONE_API_KEY", "test"),
        ]
        
        for env_var, test_pattern in test_patterns:
            value = os.getenv(env_var, "")
            if test_pattern in value.lower():
                self.errors.append(
                    f"{env_var} appears to contain test/development values in production"
                )


def validate_startup_security() -> None:
    """
    Validate security configuration at startup.
    
    Raises:
        SecurityValidationError: If critical security validations fail
    """
    validator = StartupSecurityValidator()
    is_valid, errors, warnings = validator.validate_all()
    
    # Log warnings
    for warning in warnings:
        logger.warning(f"Security Warning: {warning}")
    
    # Log and raise errors
    if not is_valid:
        logger.error("Security validation failed at startup:")
        for error in errors:
            logger.error(f"  - {error}")
        
        raise SecurityValidationError(
            f"Security validation failed with {len(errors)} error(s). "
            "Please fix the security configuration before starting the application."
        )
    
    logger.info("✅ Security validation passed - application startup authorized")


def get_security_status() -> Dict[str, any]:
    """
    Get current security validation status.
    
    Returns:
        Dictionary containing security status information
    """
    validator = StartupSecurityValidator()
    is_valid, errors, warnings = validator.validate_all()
    
    return {
        "is_valid": is_valid,
        "environment": validator.app_env,
        "errors": errors,
        "warnings": warnings,
        "error_count": len(errors),
        "warning_count": len(warnings),
    }
