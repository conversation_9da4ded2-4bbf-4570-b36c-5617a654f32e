"""JWT verification middleware for the PI Lawyer AI system."""
import logging
import os
from typing import Any, Awaitable, Callable, Dict, Optional

from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request, status
from fastapi.responses import JSONResponse
from jose import JWTError, jwt

from ..models.auth import UserContext

logger = logging.getLogger(__name__)


async def verify_jwt_middleware(
    request: Request, call_next: Callable[[Request], Awaitable[Any]]
) -> Any:
    """
    Middleware to verify JWT tokens and extract user context.

    Args:
        request: The incoming request
        call_next: The next middleware or route handler

    Returns:
        The response from the next middleware or route handler
    """
    # Skip JWT verification for public endpoints
    if _is_public_endpoint(request.url.path):
        return await call_next(request)

    # Skip JWT verification for CopilotKit endpoint (handled separately)
    if request.url.path == "/copilotkit":
        return await call_next(request)

    # Extract token from Authorization header
    token = _extract_token(request)
    if not token:
        logger.warning(f"No JWT token provided for path: {request.url.path}")
        # For development, allow requests without tokens
        if os.getenv("APP_ENV", "development") == "development":
            logger.info("Development mode: proceeding without authentication")
            # Set default user context for development
            request.state.user_id = "00000000-0000-0000-0000-000000000000"
            request.state.tenant_id = "00000000-0000-0000-0000-000000000000"
            request.state.role = "user"

            # Create user context object
            user_context = UserContext(
                user_id="00000000-0000-0000-0000-000000000000",
                email="<EMAIL>",
                role="user",
                tenant_id="00000000-0000-0000-0000-000000000000",
                permissions=[],
                is_authenticated=True,
            )
            request.state.user = user_context

            return await call_next(request)

        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={"detail": "Authentication required"},
        )

    # Verify token and extract claims
    try:
        claims = _verify_token(token)

        # Add user context to request state
        request.state.user_id = claims.get("sub")
        request.state.tenant_id = claims.get("tenant_id")
        request.state.role = claims.get("role", "user")
        request.state.email = claims.get("email")

        # Create user context object
        user_context = UserContext(
            user_id=claims.get("sub"),
            email=claims.get("email"),
            role=claims.get("role", "user"),
            tenant_id=claims.get("tenant_id"),
            permissions=claims.get("permissions", []),
            is_authenticated=True,
        )
        request.state.user = user_context

        # Proceed with the request
        response = await call_next(request)
        return response

    except JWTError as e:
        logger.warning(f"JWT verification failed: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={"detail": "Invalid authentication token"},
        )
    except Exception as e:
        logger.error(f"Error in JWT middleware: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"detail": "Internal server error"},
        )


def _is_public_endpoint(path: str) -> bool:
    """
    Check if the path is a public endpoint that doesn't require authentication.

    Args:
        path: The request path

    Returns:
        True if the path is a public endpoint, False otherwise
    """
    public_paths = [
        "/health",
        "/docs",
        "/redoc",
        "/openapi.json",
        "/config/check",
        "/public",  # Added for testing
    ]

    return any(path.startswith(public_path) for public_path in public_paths)


def _extract_token(request: Request) -> Optional[str]:
    """
    Extract JWT token from the Authorization header.

    Args:
        request: The incoming request

    Returns:
        The JWT token or None if not found
    """
    auth_header = request.headers.get("Authorization")
    if not auth_header:
        return None

    parts = auth_header.split()
    if len(parts) != 2 or parts[0].lower() != "bearer":
        return None

    return parts[1]


def _verify_token(token: str) -> Dict[str, Any]:
    """
    Verify JWT token and extract claims.

    Args:
        token: The JWT token

    Returns:
        The JWT claims

    Raises:
        JWTError: If token verification fails
    """
    jwt_secret = os.environ.get("SUPABASE_JWT_SECRET")

    if not jwt_secret:
        app_env = os.environ.get("APP_ENV", "development")
        if app_env == "production":
            logger.error("SUPABASE_JWT_SECRET environment variable is required in production")
            raise JWTError("JWT secret not configured for production environment")
        else:
            logger.error(f"SUPABASE_JWT_SECRET environment variable is required for {app_env} environment")
            raise JWTError(f"JWT secret not configured for {app_env} environment")

    # Validate secret length for security
    if len(jwt_secret) < 32:
        logger.error("SUPABASE_JWT_SECRET must be at least 32 characters long for security")
        raise JWTError("JWT secret too short - must be at least 32 characters")

    # Decode and verify the token
    payload = jwt.decode(
        token,
        jwt_secret,
        algorithms=["HS256"],
        options={"verify_signature": True},
    )

    return payload


async def verify_jwt(request: Request) -> Optional[UserContext]:
    """
    Verify JWT token from request headers for the CopilotKit endpoint.

    This function is designed to be used as a dependency for the CopilotKit endpoint.
    It extracts and verifies the JWT token from the Authorization header and adds
    the user context to the request state.

    Args:
        request: The FastAPI request object

    Returns:
        The user context if authentication is successful, None otherwise

    Raises:
        HTTPException: If token verification fails
    """
    # Skip verification in development mode if configured
    if os.getenv("APP_ENV", "development") == "development" and os.getenv("SKIP_AUTH", "false").lower() == "true":
        logger.info("Development mode: skipping authentication for CopilotKit endpoint")
        # Set default user context for development
        user_context = UserContext(
            user_id="00000000-0000-0000-0000-000000000000",
            email="<EMAIL>",
            role="user",
            tenant_id="00000000-0000-0000-0000-000000000000",
            permissions=[],
            is_authenticated=True,
        )
        request.state.user = user_context
        return user_context

    # Extract token from Authorization header
    token = _extract_token(request)
    if not token:
        logger.warning("No JWT token provided for CopilotKit endpoint")
        # For development, allow requests without tokens
        if os.getenv("APP_ENV", "development") == "development":
            logger.info("Development mode: proceeding without authentication")
            # Set default user context for development
            user_context = UserContext(
                user_id="00000000-0000-0000-0000-000000000000",
                email="<EMAIL>",
                role="user",
                tenant_id="00000000-0000-0000-0000-000000000000",
                permissions=[],
                is_authenticated=True,
            )
            request.state.user = user_context
            return user_context

        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required for CopilotKit endpoint"
        )

    # Verify token and extract claims
    try:
        claims = _verify_token(token)

        # Create user context object
        user_context = UserContext(
            user_id=claims.get("sub"),
            email=claims.get("email"),
            role=claims.get("role", "user"),
            tenant_id=claims.get("tenant_id"),
            permissions=claims.get("permissions", []),
            is_authenticated=True,
        )

        # Add user context to request state
        request.state.user = user_context

        # Log successful authentication
        logger.debug(f"JWT verification successful for user {user_context.user_id}")

        return user_context

    except JWTError as e:
        logger.warning(f"JWT verification failed for CopilotKit endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Invalid authentication token: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error verifying JWT for CopilotKit endpoint: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Authentication error: {str(e)}"
        )
