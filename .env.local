# AG-UI Configuration
NEXT_PUBLIC_AGUI_ENABLED=true
# This is the public key from your CopilotKit Cloud account
NEXT_PUBLIC_COPILOT_KEY=ck_pub_44e251a9a1f038c41298af0c89bb3aab
# This will be set when you create a Remote Endpoint in CopilotKit Cloud
CPK_ENDPOINT_SECRET=
# Agent IDs from CopilotKit Cloud
CLOUD_AGENT_ID_SUPERVISOR=
CLOUD_AGENT_ID_INTAKE=
CLOUD_AGENT_ID_DOCUMENT=
CLOUD_AGENT_ID_RESEARCH=
CLOUD_AGENT_ID_EVENT=
CLOUD_AGENT_ID_DEADLINE=

NEXT_PUBLIC_SUPABASE_URL=https://anwefmklplkjxkmzpnva.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************._mrkLcgDRn-ejKwHGEC49L2C4SVvNdBcLfJbaFwpTkU
NEXT_PUBLIC_TURNSTILE_SITE_KEY=0x4AAAAAAA7TWjsvWoeed9DQ

NEXT_PUBLIC_COPILOTKIT_RUNTIME_URL=http://localhost:3000/copilotkit



# Server-side only variables (no prefix)
TURNSTILE_SECRET_KEY=0x4AAAAAAA7TWk3iQhOJVytd19wCcp3_QGI
PINECONE_API_KEY=pcsk_2iwWdF_42RW9k72XV31RFuFmYXiq9e5nX3m2cqqG77VA27uBfwmWrXpTtnMd6wG3FkQnJe
PINECONE_ENVIRONMENT=us-east-1
PINECONE_INDEX_NAME=new-texas-laws

GCS_BUCKET_NAME=texas-laws-personalinjury
GCS_SERVICE_ACCOUNT_FILE=/Users/<USER>/Documents/Texas/texas-laws-personalinjury-16bfba9a4057.json

OPENAI_API_KEY=********************************************************************************************************************************************************************

# Super Admin Configuration
# SECURITY: Configure super admin emails via environment variable instead of hardcoded values
# Supports comma-separated format: "<EMAIL>,<EMAIL>"
# Or JSON array format: ["<EMAIL>","<EMAIL>"]
SUPER_ADMIN_EMAILS=<EMAIL>,<EMAIL>

SUPABASE_ACCESS_TOKEN=********************************************
SUPABASE_JWT_SECRET=OUxNIJzlu2O4+Wx1yJ0dK0PepsKd2oFk+5eeTkF+W1vVlMtdzZC3i9Wv6Lv1e8vxRw4ul9R9jOfnNx4K2yW9kg==
SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.XufCiYJLa531SyHDNkkVFNH0_rbPgZp4UG5yMGRabfs

DB_PASSWORD=ShakaSenghor189!

DB_USER=postgres
DB_NAME=postgres
DB_HOST=db.anwefmklplkjxkmzpnva.supabase.co
DB_PORT=5432

PDF_FOLDER_PATH=/Users/<USER>/Documents/Texas/CP

NEXT_PUBLIC_FASTAPI_URL=http://localhost:8000
