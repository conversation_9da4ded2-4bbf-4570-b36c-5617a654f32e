# API Endpoints Documentation

This document provides a comprehensive overview of all API endpoints implemented in the PI Lawyer AI application.

## Authentication & Authorization

All API endpoints use one of the following authentication methods:

1. **JWT Authentication**: For user-facing endpoints
2. **Service Token Authentication**: For internal service-to-service communication

Authorization is enforced through:
- **Role-based access control**: Different endpoints require different user roles
- **Tenant isolation**: Users can only access data from their own tenant
- **Schema separation**: Tenant data is stored in a separate `tenants` schema

## User API Endpoints

### Base URL: `/api/users`

| Method | Endpoint | Description | Required Role | Response |
|--------|----------|-------------|--------------|----------|
| GET | `/api/users` | List users with filtering | Partner, Admin | List of UserProfile objects |
| GET | `/api/users/me` | Get current user profile | Any authenticated user | UserProfile object |
| GET | `/api/users/{user_id}` | Get user by ID | Partner, Admin (for other users) | UserProfile object |
| POST | `/api/users` | Create a new user | Partner, Admin | Created UserProfile object |
| PUT | `/api/users/me` | Update current user profile | Any authenticated user | Updated UserProfile object |
| PUT | `/api/users/{user_id}` | Update a user profile | Partner, Admin | Updated UserProfile object |
| PATCH | `/api/users/{user_id}/deactivate` | Deactivate a user | Partner, Admin | Updated UserProfile object |
| PATCH | `/api/users/{user_id}/activate` | Activate a user | Partner, Admin | Updated UserProfile object |

### Service Endpoints: `/api/service/users`

| Method | Endpoint | Description | Authentication | Response |
|--------|----------|-------------|----------------|----------|
| GET | `/api/service/users/{user_id}` | Get user by ID | Service Token | UserProfile object |
| GET | `/api/service/users/auth/{auth_id}` | Get user by auth ID | Service Token | UserProfile object |

### User Model

```json
{
  "id": "uuid",
  "auth_id": "uuid",
  "email": "string",
  "first_name": "string",
  "last_name": "string",
  "role": "string",
  "tenant_id": "uuid",
  "is_active": "boolean",
  "created_at": "datetime",
  "updated_at": "datetime",
  "created_by": "uuid",
  "updated_by": "uuid"
}
```

## Client API Endpoints

### Base URL: `/api/clients`

| Method | Endpoint | Description | Required Role | Response |
|--------|----------|-------------|--------------|----------|
| GET | `/api/clients` | List clients with filtering | Any authenticated user | List of Client objects |
| GET | `/api/clients/{client_id}` | Get client by ID | Any authenticated user | Client object |
| POST | `/api/clients` | Create a new client | Any authenticated user | Created Client object |
| PUT | `/api/clients/{client_id}` | Update a client | Any authenticated user | Updated Client object |
| DELETE | `/api/clients/{client_id}` | Delete a client | Partner, Attorney | No content |
| GET | `/api/clients/search/{search_term}` | Search clients by term | Any authenticated user | List of Client objects |

### Service Endpoints: `/api/service/clients`

| Method | Endpoint | Description | Authentication | Response |
|--------|----------|-------------|----------------|----------|
| GET | `/api/service/clients/{client_id}` | Get client by ID | Service Token | Client object |
| POST | `/api/service/clients` | Create a new client | Service Token | Created Client object |

### Client Model

```json
{
  "id": "uuid",
  "first_name": "string",
  "last_name": "string",
  "email": "string",
  "phone": "string",
  "address": "string",
  "city": "string",
  "state": "string",
  "zip_code": "string",
  "status": "string",
  "notes": "string",
  "tenant_id": "uuid",
  "created_at": "datetime",
  "updated_at": "datetime",
  "created_by": "uuid",
  "updated_by": "uuid"
}
```

## Matter API Endpoints

### Base URL: `/api/matters`

| Method | Endpoint | Description | Required Role | Response |
|--------|----------|-------------|--------------|----------|
| GET | `/api/matters` | List matters with filtering | Partner, Attorney, Paralegal, Staff, Admin | List of Matter objects |
| GET | `/api/matters/{matter_id}` | Get matter by ID | Partner, Attorney, Paralegal, Staff, Admin | Matter object |
| POST | `/api/matters` | Create a new matter | Partner, Attorney, Paralegal | Created Matter object |
| PUT | `/api/matters/{matter_id}` | Update a matter | Partner, Attorney, Paralegal | Updated Matter object |
| DELETE | `/api/matters/{matter_id}` | Delete a matter | Partner | No content |
| GET | `/api/matters/{matter_id}/deadlines` | Get deadlines for a matter | Partner, Attorney, Paralegal, Staff, Admin | List of Deadline objects |

### Service Endpoints: `/api/service/matters`

| Method | Endpoint | Description | Authentication | Response |
|--------|----------|-------------|----------------|----------|
| GET | `/api/service/matters/{matter_id}` | Get matter by ID | Service Token | Matter object |
| POST | `/api/service/matters` | Create a new matter | Service Token | Created Matter object |

## Client API Endpoints

### Base URL: `/api/clients`

| Method | Endpoint | Description | Required Role | Response |
|--------|----------|-------------|--------------|----------|
| GET | `/api/clients` | List clients with filtering | Partner, Attorney, Paralegal, Staff, Admin | List of Client objects |
| GET | `/api/clients/{client_id}` | Get client by ID | Partner, Attorney, Paralegal, Staff, Admin | Client object |
| POST | `/api/clients` | Create a new client | Partner, Attorney, Paralegal | Created Client object |
| PUT | `/api/clients/{client_id}` | Update a client | Partner, Attorney, Paralegal | Updated Client object |
| DELETE | `/api/clients/{client_id}` | Delete a client | Partner | No content |

### Service Endpoints: `/api/service/clients`

| Method | Endpoint | Description | Authentication | Response |
|--------|----------|-------------|----------------|----------|
| GET | `/api/service/clients/{client_id}` | Get client by ID | Service Token | Client object |
| POST | `/api/service/clients` | Create a new client | Service Token | Created Client object |

### Matter Model

```json
{
  "id": "uuid",
  "title": "string",
  "description": "string",
  "client_id": "uuid",
  "status": "string",
  "practice_area": "personal_injury|criminal_defense|family_law|corporate_law|real_estate|tax_law|estate_planning|intellectual_property|immigration|employment_law|medical_malpractice|civil_litigation|bankruptcy|environmental_law|securities_law",
  "work_type": "litigation|transactional|advisory|intellectual_property",
  "displayLabel": "Case|Matter",
  "sensitive": "boolean",
  "tenant_id": "uuid",
  "created_at": "datetime",
  "updated_at": "datetime",
  "created_by": "uuid",
  "updated_by": "uuid",
  "matter_metadata": "object",

  // Litigation-specific fields (optional for non-litigation matters)
  "case_number": "string",
  "court_name": "string",
  "jurisdiction": "string",
  "filing_date": "datetime",
  "trial_date": "datetime"
}
```

### Dynamic Terminology System

The Matter API includes a computed `displayLabel` field that provides contextually appropriate terminology:

- **"Case"**: For litigation work (`work_type = 'litigation'`)
- **"Matter"**: For non-litigation work (`work_type != 'litigation'`)

#### Practice Area Mapping

| Practice Area | Work Type | Display Label | Context |
|---------------|-----------|---------------|---------|
| Personal Injury | litigation | Case | Court proceedings, disputes |
| Criminal Defense | litigation | Case | Criminal charges, trials |
| Civil Litigation | litigation | Case | Contract disputes, tort claims |
| Corporate Law | transactional | Matter | Business formation, M&A |
| Real Estate | transactional | Matter | Property transactions |
| Estate Planning | advisory | Matter | Wills, trusts, planning |
| Intellectual Property | intellectual_property | Matter | Patents, trademarks |

#### API Response Examples

**Litigation Matter (displays as "Case"):**
```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "title": "Smith v. Jones Auto Accident",
  "practice_area": "personal_injury",
  "work_type": "litigation",
  "displayLabel": "Case",
  "case_number": "CV-2023-001",
  "court_name": "Superior Court of California"
}
```

**Non-Litigation Matter (displays as "Matter"):**
```json
{
  "id": "456e7890-e89b-12d3-a456-************",
  "title": "ABC Corp Formation",
  "practice_area": "corporate_law",
  "work_type": "transactional",
  "displayLabel": "Matter",
  "case_number": null,
  "court_name": null
}
```

### Client Model

```json
{
  "id": "uuid",
  "tenant_id": "uuid",
  "first_name": "string",
  "last_name": "string",
  "middle_name": "string",
  "date_of_birth": "date",
  "ssn_last_four": "string",
  "email": "string",
  "phone_primary": "string",
  "phone_secondary": "string",
  "preferred_contact_method": "string",
  "address": "object",
  "occupation": "string",
  "employer": "string",
  "notes": "string",
  "emergency_contact": "object",
  "is_active": "boolean",
  "created_at": "datetime",
  "updated_at": "datetime",
  "created_by": "uuid",
  "updated_by": "uuid",
  "client_metadata": "object"
}
```

## Task API Endpoints

### Base URL: `/api/tasks`

| Method | Endpoint | Description | Required Role | Response |
|--------|----------|-------------|--------------|----------|
| GET | `/api/tasks` | List tasks with filtering | Any authenticated user | List of Task objects |
| GET | `/api/tasks/my` | List current user's tasks | Any authenticated user | List of Task objects |
| GET | `/api/tasks/{task_id}` | Get task by ID | Any authenticated user | Task object |
| POST | `/api/tasks` | Create a new task | Any authenticated user | Created Task object |
| PUT | `/api/tasks/{task_id}` | Update a task | Assigned user, Creator, Partner, Attorney | Updated Task object |
| PUT | `/api/tasks/{task_id}/complete` | Mark task as complete | Assigned user, Partner, Attorney | Updated Task object |
| DELETE | `/api/tasks/{task_id}` | Delete a task | Partner, Attorney | No content |

### Service Endpoints: `/api/service/tasks`

| Method | Endpoint | Description | Authentication | Response |
|--------|----------|-------------|----------------|----------|
| GET | `/api/service/tasks/{task_id}` | Get task by ID | Service Token | Task object |
| POST | `/api/service/tasks` | Create a new task | Service Token | Created Task object |

### Task Model

```json
{
  "id": "uuid",
  "title": "string",
  "description": "string",
  "matter_id": "uuid",
  "assigned_to": "uuid",
  "status": "string",
  "priority": "string",
  "due_date": "datetime",
  "completed_at": "datetime",
  "completed_by": "uuid",
  "task_type": "string",
  "tenant_id": "uuid",
  "created_at": "datetime",
  "updated_at": "datetime",
  "created_by": "uuid",
  "updated_by": "uuid"
}
```

## Document Search API Endpoints

### Base URL: `/api/document_search`

| Method | Endpoint | Description | Required Role | Response |
|--------|----------|-------------|--------------|----------|
| POST | `/api/document_search` | Search documents with vector search | Any authenticated user | DocumentSearchResponse object |
| GET | `/api/documents/{document_id}` | Get document by ID | Any authenticated user | Document object |
| POST | `/api/documents` | Create a new document | Any authenticated user | Created Document object |

### Service Endpoints: `/api/service/documents`

| Method | Endpoint | Description | Authentication | Response |
|--------|----------|-------------|----------------|----------|
| GET | `/api/service/documents/{document_id}` | Get document by ID | Service Token | Document object |
| POST | `/api/service/documents` | Create a new document | Service Token | Created Document object |

### Document Search Request Model

```json
{
  "query": "string",
  "document_type": "string (optional)",
  "limit": "integer (default: 10)",
  "include_preview": "boolean (default: true)"
}
```

### Document Search Response Model

```json
{
  "results": [
    {
      "id": "uuid",
      "title": "string",
      "document_type": "string",
      "score": "float",
      "preview": "string",
      "created_at": "datetime",
      "updated_at": "datetime"
    }
  ],
  "total": "integer",
  "query": "string"
}
```

## Insights API Endpoints

### Base URL: `/api/insights`

| Method | Endpoint | Description | Required Role | Response |
|--------|----------|-------------|--------------|----------|
| GET | `/api/insights` | List insights with filtering | Any authenticated user | List of Insight objects |
| GET | `/api/insights/matter/{matter_id}` | Get insights for a matter | Any authenticated user | List of Insight objects |
| GET | `/api/insights/recent` | Get recent insights | Any authenticated user | List of Insight objects |
| GET | `/api/insights/{insight_id}` | Get insight by ID | Any authenticated user | Insight object |
| POST | `/api/insights` | Create a new insight | Any authenticated user | Created Insight object |
| PATCH | `/api/insights/{insight_id}/mark-read` | Mark insight as read | Any authenticated user | Updated Insight object |
| DELETE | `/api/insights/{insight_id}` | Delete an insight | Partner, Attorney | No content |

### Service Endpoints: `/api/service/insights`

| Method | Endpoint | Description | Authentication | Response |
|--------|----------|-------------|----------------|----------|
| GET | `/api/service/insights/{insight_id}` | Get insight by ID | Service Token | Insight object |
| POST | `/api/service/insights` | Create a new insight | Service Token | Created Insight object |
| POST | `/api/service/insights/generate` | Generate insights for a matter | Service Token | List of created Insight objects |

### Insight Model

```json
{
  "id": "uuid",
  "title": "string",
  "content": "string",
  "insight_type": "string",
  "matter_id": "uuid",
  "is_read": "boolean",
  "read_by": "uuid",
  "read_at": "datetime",
  "tenant_id": "uuid",
  "created_at": "datetime",
  "updated_at": "datetime",
  "created_by": "uuid",
  "updated_by": "uuid"
}
```

## Calendar Events API Endpoints

### Base URL: `/api/calendar`

| Method | Endpoint | Description | Required Role | Response |
|--------|----------|-------------|--------------|----------|
| GET | `/api/calendar` | List calendar events with filtering | Any authenticated user | List of CalendarEvent objects |
| GET | `/api/calendar/upcoming` | Get upcoming events | Any authenticated user | List of CalendarEvent objects |
| GET | `/api/calendar/my` | Get current user's events | Any authenticated user | List of CalendarEvent objects |
| GET | `/api/calendar/matter/{matter_id}` | Get events for a matter | Any authenticated user | List of CalendarEvent objects |
| GET | `/api/calendar/{event_id}` | Get calendar event by ID | Any authenticated user | CalendarEvent object |
| POST | `/api/calendar` | Create a new calendar event | Any authenticated user | Created CalendarEvent object |
| PUT | `/api/calendar/{event_id}` | Update a calendar event | Any authenticated user | Updated CalendarEvent object |
| DELETE | `/api/calendar/{event_id}` | Delete a calendar event | Event owner, assignee, or managers | No content |

### Service Endpoints: `/api/service/calendar`

| Method | Endpoint | Description | Authentication | Response |
|--------|----------|-------------|----------------|----------|
| GET | `/api/service/calendar/{event_id}` | Get calendar event by ID | Service Token | CalendarEvent object |
| POST | `/api/service/calendar` | Create a new calendar event | Service Token | Created CalendarEvent object |
| POST | `/api/service/calendar/generate-deadlines` | Generate deadline events for a matter | Service Token | List of created CalendarEvent objects |

### Calendar Event Model

```json
{
  "id": "uuid",
  "title": "string",
  "description": "string",
  "start_time": "datetime",
  "end_time": "datetime",
  "event_type": "string",
  "location": "string",
  "matter_id": "uuid",
  "client_id": "uuid",
  "user_id": "uuid",
  "is_all_day": "boolean",
  "tenant_id": "uuid",
  "created_at": "datetime",
  "updated_at": "datetime",
  "created_by": "uuid",
  "updated_by": "uuid"
}
```

## LangGraph Agent Endpoints

### Base URL: `/api/agents`

| Method | Endpoint | Description | Authentication | Response |
|--------|----------|-------------|----------------|----------|
| POST | `/api/agents/invoke` | Invoke an agent | JWT | Agent response |
| POST | `/api/agents/invoke/stream` | Invoke an agent with streaming | JWT | Streamed agent response |

### Matter & Client CRUD Agent

The Matter & Client CRUD Agent is a LangGraph-based agent that handles creating, reading, updating, and deleting matters and clients through natural language interaction. It provides a conversational interface for matter and client management with dynamic terminology.

#### Agent Capabilities

- Create new matters and clients with detailed information
- Retrieve matter and client information with filtering options
- Update matter and client details
- Delete matters and clients with confirmation
- Enforce tenant isolation for security
- Check permissions based on user roles
- Automatically apply appropriate terminology (Case vs Matter) based on practice area

#### Usage Examples

```
User: Create a new personal injury matter for John Doe
Agent: I'll create a new personal injury matter for John Doe. Please provide a title for the matter.
User: Car accident on Highway 101
Agent: I've created a new case with the following details:

Title: Car accident on Highway 101
Client: John Doe
Status: active
Practice Area: personal_injury
Work Type: litigation
Display Label: Case (litigation work)
ID: 3f7b8a9c-1d2e-3f4a-5b6c-7d8e9f0a1b2c
```

```
User: Create a new corporate matter for ABC Corp
Agent: I'll create a new corporate matter for ABC Corp. Please provide a title for the matter.
User: LLC Formation and Operating Agreement
Agent: I've created a new matter with the following details:

Title: LLC Formation and Operating Agreement
Client: ABC Corp
Status: active
Practice Area: corporate_law
Work Type: transactional
Display Label: Matter (non-litigation work)
ID: 4g8c9b0d-2e3f-4g5h-6i7j-8k9l0m1n2o3p
```

For more details, see the [Matter & Client CRUD Agent documentation](matter_client_crud_agent.md).

## Authentication Endpoints

### Base URL: `/api/auth`

| Method | Endpoint | Description | Authentication | Response |
|--------|----------|-------------|----------------|----------|
| POST | `/api/auth/validate-token` | Validate JWT token | None | ValidationResponse object |
| POST | `/api/auth/refresh-token` | Refresh JWT token | Valid refresh token | TokenResponse object |

### Token Validation Response Model

```json
{
  "valid": "boolean",
  "user_id": "uuid (if valid)",
  "role": "string (if valid)",
  "tenant_id": "uuid (if valid)"
}
```

## Service Layer

In addition to the API endpoints, we've implemented a service layer for complex operations:

### Task Service

The `TaskService` provides high-level operations for task management:

| Method | Description | Parameters |
|--------|-------------|------------|
| `create_task_with_notification` | Create a task and notify assigned user | task_data, current_user |
| `create_standard_case_tasks` | Create default task workflow for a case | case_id, template_id, current_user |
| `get_overdue_tasks_by_tenant` | Get all overdue tasks for a tenant | tenant_id, limit |
| `assign_tasks_to_user` | Batch assign multiple tasks to a user | user_id, task_ids, current_user |

### Future Services

Additional services planned for implementation:

1. **Insight Generation Service**: AI-powered analysis of case information to generate insights
2. **Calendar Management Service**: Deadline calculation based on case type and jurisdictions
3. **Document Generation Service**: Template-based document generation for legal workflows

## Frontend Integration

These API endpoints are consumed by Next.js API routes and React components:

### Next.js API Routes

| Route | Description | Backend Endpoint |
|-------|-------------|------------------|
| `GET /api/documents/[id]` | Get document by ID | `/api/service/documents/{id}` |
| `PUT /api/documents/[id]` | Update document | `/api/service/documents/{id}` |
| `GET /api/clients` | List clients | `/api/clients` |
| `GET /api/clients/[id]` | Get client details | `/api/clients/{id}` |

### React Components

| Component | Description | API Routes Used |
|-----------|-------------|-----------------|
| `ClientsPage` | List and search clients | `/api/clients` |
| `ClientDetailsPage` | View client details | `/api/clients/[id]`, `/api/clients/[id]/cases`, `/api/clients/[id]/documents` |
| `AuthForm` | Login, registration, password reset | Supabase Auth API |

## Authentication & Security

All API endpoints implement the following security measures:

1. **JWT Validation**: User tokens are validated using the Supabase JWT secret
2. **Service Token Validation**: Service tokens are validated against environment variables
3. **Role-Based Access Control**: Each endpoint checks user roles for appropriate permissions
4. **Tenant Isolation**: Data access is restricted to the user's tenant
5. **Schema Separation**: Tenant data stored in `tenants` schema with proper RLS policies
6. **Logging**: All access and errors are logged for audit purposes
7. **Turnstile Protection**: Cloudflare Turnstile implemented for public-facing forms

For more details on the authentication system, see [service-mesh-authentication.md](./service-mesh-authentication.md).
