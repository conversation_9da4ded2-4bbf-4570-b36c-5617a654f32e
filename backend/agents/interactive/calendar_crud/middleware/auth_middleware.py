"""
Authentication middleware for Calendar CRUD Agent.

This middleware validates JWT tokens, extracts firm_id (tenant_id), and injects it into
request.state for use by calendar endpoints. It also implements tenant isolation checks
to ensure users can only access their own tenant's data.
"""

import logging
import os
from typing import Any, Awaitable, Callable, Dict, List, Optional

import jwt
from fastapi import HTTPException, Request, Response, status
from fastapi.responses import JSONResponse
from jwt.exceptions import PyJWTError
from pydantic import BaseModel, Field
from starlette.middleware.base import BaseHTTPMiddleware

# Configure logging
logger = logging.getLogger(__name__)


class UserContext(BaseModel):
    """User context extracted from JWT token."""

    user_id: str = Field(..., description="User ID")
    email: Optional[str] = Field(None, description="User email")
    role: str = Field(..., description="User role")
    firm_id: Optional[str] = Field(None, description="Firm/Tenant ID")
    permissions: List[str] = Field(default_factory=list, description="User permissions")
    is_authenticated: bool = Field(
        True, description="Whether the user is authenticated"
    )


def _is_public_endpoint(path: str) -> bool:
    """
    Check if the path is a public endpoint that doesn't require authentication.

    Args:
        path: The request path

    Returns:
        bool: True if the path is a public endpoint, False otherwise
    """
    public_paths = [
        "/health",
        "/docs",
        "/redoc",
        "/openapi.json",
        "/calendar/connect",  # OAuth redirect endpoint
    ]

    # Check if the path is in the list of public paths
    for public_path in public_paths:
        if path == public_path or path.startswith(f"{public_path}/"):
            return True

    return False


def _extract_token(request: Request) -> Optional[str]:
    """
    Extract JWT token from request headers.

    Args:
        request: The incoming request

    Returns:
        Optional[str]: The JWT token if found, None otherwise
    """
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        return None

    return auth_header.split(" ")[1]


def _verify_token(token: str) -> Dict[str, Any]:
    """
    Verify JWT token and extract claims.

    Args:
        token: The JWT token

    Returns:
        Dict[str, Any]: The JWT claims

    Raises:
        JWTError: If token verification fails
    """
    jwt_secret = os.environ.get("SUPABASE_JWT_SECRET")

    if not jwt_secret:
        app_env = os.environ.get("APP_ENV", "development")
        if app_env == "production":
            logger.error("SUPABASE_JWT_SECRET environment variable is required in production")
            raise PyJWTError("JWT secret not configured for production environment")
        else:
            logger.error(f"SUPABASE_JWT_SECRET environment variable is required for {app_env} environment")
            raise PyJWTError(f"JWT secret not configured for {app_env} environment")

    # Validate secret length for security
    if len(jwt_secret) < 32:
        logger.error("SUPABASE_JWT_SECRET must be at least 32 characters long for security")
        raise PyJWTError("JWT secret too short - must be at least 32 characters")

    # Decode and verify the token
    payload = jwt.decode(
        token,
        jwt_secret,
        algorithms=["HS256"],
        options={"verify_signature": True},
    )

    return payload


class AuthMiddleware(BaseHTTPMiddleware):
    """
    Middleware to verify JWT tokens, extract firm_id, and implement tenant isolation.

    This middleware is implemented as a class that inherits from BaseHTTPMiddleware
    for better integration with FastAPI's middleware system.
    """

    async def dispatch(
        self, request: Request, call_next: Callable[[Request], Awaitable[Response]]
    ) -> Response:
        """
        Process a request and apply authentication.

        Args:
            request: The incoming request
            call_next: The next middleware or route handler

        Returns:
            Response: The response from the next middleware or route handler
        """
        return await calendar_auth_middleware(request, call_next)


async def calendar_auth_middleware(
    request: Request, call_next: Callable[[Request], Awaitable[Response]]
) -> Response:
    """
    Middleware to verify JWT tokens, extract firm_id, and implement tenant isolation.

    Args:
        request: The incoming request
        call_next: The next middleware or route handler

    Returns:
        Response: The response from the next middleware or route handler
    """
    # Skip JWT verification for public endpoints
    if _is_public_endpoint(request.url.path):
        return await call_next(request)

    # Extract token from Authorization header
    token = _extract_token(request)
    if not token:
        logger.warning(f"No JWT token provided for path: {request.url.path}")
        # For development, allow requests without tokens
        if os.getenv("APP_ENV", "development") == "development":
            logger.info("Development mode: proceeding without authentication")
            # Set default user context for development
            request.state.user_id = "00000000-0000-0000-0000-000000000000"
            request.state.firm_id = "00000000-0000-0000-0000-000000000000"
            request.state.role = "user"

            # Create user context object
            user_context = UserContext(
                user_id="00000000-0000-0000-0000-000000000000",
                email="<EMAIL>",
                role="user",
                firm_id="00000000-0000-0000-0000-000000000000",
                permissions=[],
                is_authenticated=True,
            )
            request.state.user = user_context

            # Log the authentication bypass
            logger.debug(
                f"Development mode: bypassing authentication for {request.url.path}"
            )

            # Proceed with the request
            return await call_next(request)
        else:
            # In production, return 401 Unauthorized
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Authentication required"},
            )

    # Verify token and extract claims
    try:
        claims = _verify_token(token)

        # Extract firm_id from tenant_id in the token
        firm_id = claims.get("tenant_id")
        if not firm_id:
            logger.warning("No firm_id/tenant_id found in JWT token")
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Invalid authentication token: missing tenant_id"},
            )

        # Add user context to request state
        request.state.user_id = claims.get("sub")
        request.state.firm_id = firm_id
        request.state.role = claims.get("role", "user")
        request.state.email = claims.get("email")

        # Create user context object
        user_context = UserContext(
            user_id=claims.get("sub"),
            email=claims.get("email"),
            role=claims.get("role", "user"),
            firm_id=firm_id,
            permissions=claims.get("permissions", []),
            is_authenticated=True,
        )
        request.state.user = user_context

        # Log successful authentication
        logger.debug(
            f"JWT verification successful for user {user_context.user_id}, "
            f"firm {firm_id}"
        )

        # Proceed with the request
        response = await call_next(request)
        return response

    except PyJWTError as e:
        logger.warning(f"JWT verification failed: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={"detail": "Invalid authentication token"},
        )
    except Exception as e:
        logger.error(f"Error in JWT middleware: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"detail": "Internal server error"},
        )


# Dependency for route-level authentication
async def get_current_user(request: Request) -> UserContext:
    """
    Get the current authenticated user from request state.

    This function is designed to be used as a dependency in route handlers.

    Args:
        request: The FastAPI request object

    Returns:
        UserContext: The authenticated user context

    Raises:
        HTTPException: If the user is not authenticated
    """
    if not hasattr(request.state, "user"):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
        )

    return request.state.user


# Dependency for getting firm_id
async def get_firm_id(request: Request) -> str:
    """
    Get the firm_id from request state.

    This function is designed to be used as a dependency in route handlers.

    Args:
        request: The FastAPI request object

    Returns:
        str: The firm_id

    Raises:
        HTTPException: If the user is not authenticated
    """
    if not hasattr(request.state, "firm_id"):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
        )

    return request.state.firm_id


# Dependency for checking tenant isolation
async def verify_tenant_access(request: Request, firm_id: str) -> None:
    """
    Verify that the authenticated user has access to the specified firm/tenant.

    This function is designed to be used as a dependency in route handlers.

    Args:
        request: The FastAPI request object
        firm_id: The firm/tenant ID to check access for

    Raises:
        HTTPException: If the user does not have access to the specified firm/tenant
    """
    if not hasattr(request.state, "firm_id"):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
        )

    # Check if the user has access to the specified firm/tenant
    if request.state.firm_id != firm_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access to the specified firm/tenant is not allowed",
        )
