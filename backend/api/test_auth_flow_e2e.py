#!/usr/bin/env python3
"""
End-to-End Authentication Flow Test

This script tests the complete authentication flow from JWT token generation
through authorization checks to ensure the system works correctly in production.
"""

import os
import sys
import json
import uuid
import time
from datetime import datetime, timedelta, timezone
from typing import Dict, Any

import jwt
import requests

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from backend.api.dependencies.auth import UserContext


class AuthFlowTester:
    """End-to-end authentication flow tester."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        # Use the same environment variable as the auth dependencies
        self.jwt_secret = os.getenv("SUPABASE_JWT_SECRET", "test-secret-key-for-development-only-min-32-chars")
        self.test_tenant_id = str(uuid.uuid4())
        self.test_user_id = str(uuid.uuid4())
        
    def generate_test_jwt(self, role: str = "attorney", email: str = "<EMAIL>") -> str:
        """Generate a test JWT token with specified role and claims."""
        now = datetime.now(timezone.utc)
        payload = {
            "sub": self.test_user_id,
            "email": email,
            "name": "Test User",
            "tenant_id": self.test_tenant_id,
            "role": role,
            "is_super_admin": role == "superadmin",
            "iat": int(now.timestamp()),
            "exp": int((now + timedelta(hours=1)).timestamp())
        }
        
        return jwt.encode(payload, self.jwt_secret, algorithm="HS256")
    
    def make_authenticated_request(self, method: str, endpoint: str, token: str, **kwargs) -> requests.Response:
        """Make an authenticated request to the API."""
        headers = kwargs.get("headers", {})
        headers["Authorization"] = f"Bearer {token}"
        kwargs["headers"] = headers
        
        url = f"{self.base_url}{endpoint}"
        return requests.request(method, url, **kwargs)
    
    def test_jwt_generation(self) -> bool:
        """Test JWT token generation and validation."""
        print("Testing JWT token generation...")
        
        try:
            # Generate token for different roles
            roles = ["client", "staff", "paralegal", "attorney", "partner", "superadmin"]
            
            for role in roles:
                token = self.generate_test_jwt(role=role)
                
                # Decode and verify token
                decoded = jwt.decode(token, self.jwt_secret, algorithms=["HS256"])
                
                if decoded["role"] != role:
                    print(f"❌ Token role mismatch for {role}")
                    return False
                
                if decoded["tenant_id"] != self.test_tenant_id:
                    print(f"❌ Token tenant_id mismatch for {role}")
                    return False
                
                if decoded["is_super_admin"] != (role == "superadmin"):
                    print(f"❌ Token is_super_admin mismatch for {role}")
                    return False
            
            print("✅ JWT token generation working correctly")
            return True
            
        except Exception as e:
            print(f"❌ JWT generation failed: {e}")
            return False
    
    def test_token_expiration(self) -> bool:
        """Test expired token handling."""
        print("Testing expired token handling...")
        
        try:
            # Generate expired token
            now = datetime.now(timezone.utc)
            payload = {
                "sub": self.test_user_id,
                "email": "<EMAIL>",
                "name": "Test User",
                "tenant_id": self.test_tenant_id,
                "role": "attorney",
                "is_super_admin": False,
                "iat": int((now - timedelta(hours=2)).timestamp()),
                "exp": int((now - timedelta(hours=1)).timestamp())  # Expired 1 hour ago
            }
            
            expired_token = jwt.encode(payload, self.jwt_secret, algorithm="HS256")
            
            # Try to decode expired token
            try:
                jwt.decode(expired_token, self.jwt_secret, algorithms=["HS256"])
                print("❌ Expired token should not be valid")
                return False
            except jwt.ExpiredSignatureError:
                print("✅ Expired token correctly rejected")
                return True
            
        except Exception as e:
            print(f"❌ Token expiration test failed: {e}")
            return False
    
    def test_invalid_signature(self) -> bool:
        """Test invalid signature handling."""
        print("Testing invalid signature handling...")
        
        try:
            # Generate token with wrong secret
            wrong_secret = "wrong-secret-key-for-testing-purposes-only"
            token = jwt.encode({
                "sub": self.test_user_id,
                "email": "<EMAIL>",
                "role": "attorney",
                "tenant_id": self.test_tenant_id,
                "exp": int((datetime.now(timezone.utc) + timedelta(hours=1)).timestamp())
            }, wrong_secret, algorithm="HS256")
            
            # Try to decode with correct secret
            try:
                jwt.decode(token, self.jwt_secret, algorithms=["HS256"])
                print("❌ Invalid signature should be rejected")
                return False
            except jwt.InvalidSignatureError:
                print("✅ Invalid signature correctly rejected")
                return True
            
        except Exception as e:
            print(f"❌ Invalid signature test failed: {e}")
            return False
    
    def test_role_based_authorization(self) -> bool:
        """Test role-based authorization with different user roles."""
        print("Testing role-based authorization...")
        
        # Test data: (role, should_have_admin_access, should_have_super_admin_access)
        test_cases = [
            ("client", False, False),
            ("staff", False, False),
            ("paralegal", False, False),
            ("attorney", False, False),
            ("partner", True, False),
            ("superadmin", True, True),
        ]
        
        try:
            from backend.api.dependencies.authorization import has_permission, Permission, UserRole
            
            for role, should_have_admin, should_have_super_admin in test_cases:
                # Create user context
                user = UserContext(
                    id=uuid.UUID(self.test_user_id),
                    email=f"test-{role}@example.com",
                    name=f"Test {role.title()}",
                    tenant_id=uuid.UUID(self.test_tenant_id),
                    role=role
                )
                
                # Test admin access
                has_admin = has_permission(user, Permission.ADMIN_ACCESS)
                if has_admin != should_have_admin:
                    print(f"❌ {role} admin access: expected {should_have_admin}, got {has_admin}")
                    return False
                
                # Test super admin access
                has_super_admin = has_permission(user, Permission.SUPER_ADMIN_ACCESS)
                if has_super_admin != should_have_super_admin:
                    print(f"❌ {role} super admin access: expected {should_have_super_admin}, got {has_super_admin}")
                    return False
            
            print("✅ Role-based authorization working correctly")
            return True
            
        except Exception as e:
            print(f"❌ Role-based authorization test failed: {e}")
            return False
    
    def test_tenant_isolation(self) -> bool:
        """Test tenant isolation in authorization."""
        print("Testing tenant isolation...")
        
        try:
            from backend.api.dependencies.authorization import has_permission, Permission
            
            tenant1_id = str(uuid.uuid4())
            tenant2_id = str(uuid.uuid4())
            
            # Create users from different tenants
            user1 = UserContext(
                id=uuid.uuid4(),
                email="<EMAIL>",
                name="User 1",
                tenant_id=uuid.UUID(tenant1_id),
                role="attorney"
            )
            
            user2 = UserContext(
                id=uuid.uuid4(),
                email="<EMAIL>",
                name="User 2", 
                tenant_id=uuid.UUID(tenant2_id),
                role="attorney"
            )
            
            # Both should have same permissions within their tenants
            perm = Permission.READ_MATTER
            
            if not has_permission(user1, perm):
                print("❌ User1 should have READ_MATTER permission")
                return False
            
            if not has_permission(user2, perm):
                print("❌ User2 should have READ_MATTER permission")
                return False
            
            # Verify they have different tenant IDs
            if user1.tenant_id == user2.tenant_id:
                print("❌ Users should have different tenant IDs")
                return False
            
            print("✅ Tenant isolation working correctly")
            return True
            
        except Exception as e:
            print(f"❌ Tenant isolation test failed: {e}")
            return False
    
    def test_authentication_dependencies(self) -> bool:
        """Test authentication dependencies work correctly."""
        print("Testing authentication dependencies...")
        
        try:
            from backend.api.dependencies.auth import _get_jwt_secret, _verify_jwt_token
            
            # Test JWT secret retrieval
            secret = _get_jwt_secret()
            if not secret or len(secret) < 32:
                print("❌ JWT secret should be at least 32 characters")
                return False
            
            # Test token verification
            valid_token = self.generate_test_jwt(role="attorney")
            
            try:
                payload = _verify_jwt_token(valid_token)
                if payload["role"] != "attorney":
                    print("❌ Token verification should return correct role")
                    return False
            except Exception as e:
                print(f"❌ Valid token should be accepted: {e}")
                return False
            
            # Test invalid token
            try:
                _verify_jwt_token("invalid.token.here")
                print("❌ Invalid token should be rejected")
                return False
            except Exception:
                # Expected to fail
                pass
            
            print("✅ Authentication dependencies working correctly")
            return True
            
        except Exception as e:
            print(f"❌ Authentication dependencies test failed: {e}")
            return False


def main():
    """Run all end-to-end authentication tests."""
    print("🔐 End-to-End Authentication Flow Test")
    print("=" * 60)
    
    tester = AuthFlowTester()
    
    tests = [
        tester.test_jwt_generation,
        tester.test_token_expiration,
        tester.test_invalid_signature,
        tester.test_role_based_authorization,
        tester.test_tenant_isolation,
        tester.test_authentication_dependencies,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ {test.__name__} failed")
        except Exception as e:
            print(f"❌ {test.__name__} crashed: {e}")
        print()  # Add spacing between tests
    
    print("=" * 60)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All authentication flow tests passed!")
        print("✅ Authentication system is ready for production!")
        return 0
    else:
        print("💥 Some authentication flow tests failed!")
        print("❌ Authentication system needs fixes before production!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
