"""
Tenant management API routes.

Handles tenant creation with automatic MCP provisioning.
"""

import logging
from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, Field

from backend.api.dependencies.authorization import require_super_admin_permission
from backend.middleware.auth_middleware import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/tenants", tags=["tenants"])


class TenantCreateRequest(BaseModel):
    """Request model for tenant creation."""
    name: str = Field(..., description="Firm name")
    state_bar_number: str = Field(..., description="State bar number")
    firm_type: str = Field(..., description="Type of firm")
    year_established: int = Field(None, description="Year firm was established")
    tax_id: str = Field(None, description="Tax ID")
    primary_email: str = Field(..., description="Primary email address")
    secondary_email: str = Field(None, description="Secondary email address")
    phone: str = Field(..., description="Phone number")
    fax: str = Field(None, description="Fax number")
    website_url: str = Field(None, description="Website URL")
    address: Dict[str, Any] = Field(
        default_factory=dict, description="Address information"
    )
    practice_areas: list[str] = Field(
        default_factory=list, description="Practice areas"
    )
    specializations: list[str] = Field(
        default_factory=list, description="Specializations"
    )
    admin_user_id: str = Field(None, description="Admin user ID")
    subscription_tier: str = Field("basic", description="Subscription tier")
    subscription_status: str = Field("active", description="Subscription status")
    settings: Dict[str, Any] = Field(
        default_factory=dict, description="Firm settings"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata"
    )
    jurisdiction_settings: Dict[str, Any] = Field(
        default_factory=dict, description="Jurisdiction settings"
    )


class TenantCreateResponse(BaseModel):
    """Response model for tenant creation."""
    id: str
    tenant_id: str
    name: str
    mcp_status: str
    mcp_secret_path: str = None
    created_at: str
    updated_at: str
    message: str = "Tenant created successfully"


@router.post("/", response_model=TenantCreateResponse)
async def create_tenant(
    request: TenantCreateRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    _: Dict[str, Any] = Depends(require_super_admin_permission)
) -> TenantCreateResponse:
    """
    Create a new tenant with automatic MCP provisioning.
    
    This endpoint creates a new tenant and automatically provisions
    MCP (Model Context Protocol) access by creating API keys and
    storing them securely in Google Secret Manager.
    """
    try:
        logger.info(f"Creating tenant: {request.name}")
        
        # Import here to avoid circular imports
        from frontend.src.lib.supabase.client import createClient
        from frontend.src.services.tenantService import createTenantService
        
        # Create Supabase client
        supabase = createClient()
        
        # Create tenant service
        tenant_service = createTenantService(supabase)
        
        # Convert request to tenant input
        tenant_input = {
            "name": request.name,
            "state_bar_number": request.state_bar_number,
            "firm_type": request.firm_type,
            "year_established": request.year_established,
            "tax_id": request.tax_id,
            "primary_email": request.primary_email,
            "secondary_email": request.secondary_email,
            "phone": request.phone,
            "fax": request.fax,
            "website_url": request.website_url,
            "address": request.address,
            "practice_areas": request.practice_areas,
            "specializations": request.specializations,
            "admin_user_id": request.admin_user_id or current_user.get("id"),
            "subscription_tier": request.subscription_tier,
            "subscription_status": request.subscription_status,
            "settings": request.settings,
            "metadata": request.metadata,
            "jurisdiction_settings": request.jurisdiction_settings,
        }
        
        # Create tenant with automatic MCP provisioning
        tenant = await tenant_service.createTenant(tenant_input)
        
        logger.info(f"Tenant created successfully: {tenant['tenant_id']}")
        
        return TenantCreateResponse(
            id=tenant["id"],
            tenant_id=tenant["tenant_id"],
            name=tenant["name"],
            mcp_status=tenant["mcp_status"],
            mcp_secret_path=tenant.get("mcp_secret_path"),
            created_at=tenant["created_at"],
            updated_at=tenant["updated_at"],
            message="Tenant created successfully with MCP provisioning"
        )
        
    except Exception as e:
        logger.error(f"Failed to create tenant: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create tenant: {str(e)}"
        ) from e


@router.get("/{tenant_id}")
async def get_tenant(
    tenant_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """Get tenant by ID."""
    try:
        # Import here to avoid circular imports
        from frontend.src.lib.supabase.client import createClient
        from frontend.src.services.tenantService import createTenantService
        
        # Create Supabase client
        supabase = createClient()
        
        # Create tenant service
        tenant_service = createTenantService(supabase)
        
        # Get tenant
        tenant = await tenant_service.getTenantById(tenant_id)
        
        if not tenant:
            raise HTTPException(status_code=404, detail="Tenant not found")
        
        return tenant
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get tenant: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get tenant: {str(e)}"
        ) from e


@router.post("/{tenant_id}/retry-mcp")
async def retry_mcp_provisioning(
    tenant_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> TenantCreateResponse:
    """Retry MCP provisioning for a failed tenant."""
    try:
        logger.info(f"Retrying MCP provisioning for tenant: {tenant_id}")
        
        # Import here to avoid circular imports
        from frontend.src.lib.supabase.client import createClient
        from frontend.src.services.tenantService import createTenantService
        
        # Create Supabase client
        supabase = createClient()
        
        # Create tenant service
        tenant_service = createTenantService(supabase)
        
        # Retry MCP provisioning
        tenant = await tenant_service.retryMcpProvisioning(tenant_id)
        
        logger.info(f"MCP provisioning retry completed: {tenant_id}")
        
        return TenantCreateResponse(
            id=tenant["id"],
            tenant_id=tenant["tenant_id"],
            name=tenant["name"],
            mcp_status=tenant["mcp_status"],
            mcp_secret_path=tenant.get("mcp_secret_path"),
            created_at=tenant["created_at"],
            updated_at=tenant["updated_at"],
            message="MCP provisioning retry completed"
        )
        
    except Exception as e:
        logger.error(f"Failed to retry MCP provisioning: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retry MCP provisioning: {str(e)}"
        ) from e
