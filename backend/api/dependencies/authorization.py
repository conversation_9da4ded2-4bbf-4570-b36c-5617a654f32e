"""
Authorization dependencies for API endpoints.

This module provides FastAPI dependencies for role-based access control (RBAC)
and resource-level authorization checks.
"""

import logging
from enum import Enum
from typing import List, Set

from fastapi import Depends, HTTPException, status

from .auth import UserContext, get_current_user

logger = logging.getLogger(__name__)


class UserRole(str, Enum):
    """User roles in the system."""
    SUPERADMIN = "superadmin"
    PARTNER = "partner"
    ATTORNEY = "attorney"
    PARALEGAL = "paralegal"
    STAFF = "staff"
    CLIENT = "client"


class Permission(str, Enum):
    """System permissions."""
    # Tenant management
    CREATE_TENANT = "create_tenant"
    MANAGE_TENANT = "manage_tenant"
    
    # User management
    CREATE_USER = "create_user"
    MANAGE_USERS = "manage_users"
    
    # Matter/Case management
    CREATE_MATTER = "create_matter"
    READ_MATTER = "read_matter"
    UPDATE_MATTER = "update_matter"
    DELETE_MATTER = "delete_matter"
    
    # Document management
    CREATE_DOCUMENT = "create_document"
    READ_DOCUMENT = "read_document"
    UPDATE_DOCUMENT = "update_document"
    DELETE_DOCUMENT = "delete_document"
    
    # Calendar management
    MANAGE_CALENDAR = "manage_calendar"
    READ_CALENDAR = "read_calendar"
    
    # Subscription management
    MANAGE_SUBSCRIPTION = "manage_subscription"
    READ_SUBSCRIPTION = "read_subscription"
    
    # Admin operations
    ADMIN_ACCESS = "admin_access"
    SUPER_ADMIN_ACCESS = "super_admin_access"
    
    # Metrics and monitoring
    READ_METRICS = "read_metrics"
    MANAGE_METRICS = "manage_metrics"


# Role-based permission matrix
ROLE_PERMISSIONS: dict[UserRole, Set[Permission]] = {
    UserRole.SUPERADMIN: {
        # Super admin has all permissions
        Permission.CREATE_TENANT,
        Permission.MANAGE_TENANT,
        Permission.CREATE_USER,
        Permission.MANAGE_USERS,
        Permission.CREATE_MATTER,
        Permission.READ_MATTER,
        Permission.UPDATE_MATTER,
        Permission.DELETE_MATTER,
        Permission.CREATE_DOCUMENT,
        Permission.READ_DOCUMENT,
        Permission.UPDATE_DOCUMENT,
        Permission.DELETE_DOCUMENT,
        Permission.MANAGE_CALENDAR,
        Permission.READ_CALENDAR,
        Permission.MANAGE_SUBSCRIPTION,
        Permission.READ_SUBSCRIPTION,
        Permission.ADMIN_ACCESS,
        Permission.SUPER_ADMIN_ACCESS,
        Permission.READ_METRICS,
        Permission.MANAGE_METRICS,
    },
    UserRole.PARTNER: {
        # Partner has most permissions within their tenant
        Permission.MANAGE_TENANT,
        Permission.CREATE_USER,
        Permission.MANAGE_USERS,
        Permission.CREATE_MATTER,
        Permission.READ_MATTER,
        Permission.UPDATE_MATTER,
        Permission.DELETE_MATTER,
        Permission.CREATE_DOCUMENT,
        Permission.READ_DOCUMENT,
        Permission.UPDATE_DOCUMENT,
        Permission.DELETE_DOCUMENT,
        Permission.MANAGE_CALENDAR,
        Permission.READ_CALENDAR,
        Permission.MANAGE_SUBSCRIPTION,
        Permission.READ_SUBSCRIPTION,
        Permission.ADMIN_ACCESS,
        Permission.READ_METRICS,
    },
    UserRole.ATTORNEY: {
        # Attorney can manage assigned matters and documents
        Permission.CREATE_MATTER,
        Permission.READ_MATTER,
        Permission.UPDATE_MATTER,
        Permission.CREATE_DOCUMENT,
        Permission.READ_DOCUMENT,
        Permission.UPDATE_DOCUMENT,
        Permission.MANAGE_CALENDAR,
        Permission.READ_CALENDAR,
        Permission.READ_SUBSCRIPTION,
    },
    UserRole.PARALEGAL: {
        # Paralegal can assist with matters and documents
        Permission.READ_MATTER,
        Permission.UPDATE_MATTER,
        Permission.CREATE_DOCUMENT,
        Permission.READ_DOCUMENT,
        Permission.UPDATE_DOCUMENT,
        Permission.READ_CALENDAR,
        Permission.READ_SUBSCRIPTION,
    },
    UserRole.STAFF: {
        # Staff has read-only access to most resources
        Permission.READ_MATTER,
        Permission.READ_DOCUMENT,
        Permission.READ_CALENDAR,
        Permission.READ_SUBSCRIPTION,
    },
    UserRole.CLIENT: {
        # Client can only read their own matters and documents
        Permission.READ_MATTER,
        Permission.READ_DOCUMENT,
    },
}


def has_permission(user: UserContext, permission: Permission) -> bool:
    """
    Check if a user has a specific permission.
    
    Args:
        user: The user context
        permission: The permission to check
        
    Returns:
        bool: True if user has the permission
    """
    try:
        user_role = UserRole(user.role.lower())
        return permission in ROLE_PERMISSIONS.get(user_role, set())
    except ValueError:
        logger.warning(f"Unknown user role: {user.role}")
        return False


def require_permission(permission: Permission):
    """
    Dependency factory that creates a dependency requiring a specific permission.
    
    Args:
        permission: The required permission
        
    Returns:
        FastAPI dependency function
    """
    async def _check_permission(
        current_user: UserContext = Depends(get_current_user)
    ) -> UserContext:
        if not has_permission(current_user, permission):
            logger.warning(
                f"Permission denied: user {current_user.email} "
                f"(role: {current_user.role}) lacks permission {permission.value}"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission denied: {permission.value} required"
            )
        return current_user
    
    return _check_permission


def require_role(allowed_roles: List[UserRole]):
    """
    Dependency factory that creates a dependency requiring specific roles.
    
    Args:
        allowed_roles: List of allowed user roles
        
    Returns:
        FastAPI dependency function
    """
    async def _check_role(
        current_user: UserContext = Depends(get_current_user)
    ) -> UserContext:
        try:
            user_role = UserRole(current_user.role.lower())
            if user_role not in allowed_roles:
                logger.warning(
                    f"Role access denied: user {current_user.email} "
                    f"(role: {current_user.role}) not in allowed roles: "
                    f"{[role.value for role in allowed_roles]}"
                )
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Access denied: role {current_user.role} not authorized"
                )
        except ValueError as e:
            logger.warning(f"Unknown user role: {current_user.role}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied: invalid role {current_user.role}"
            ) from e
        
        return current_user
    
    return _check_role


# Convenience dependencies for common permission checks
require_super_admin_permission = require_permission(
    Permission.SUPER_ADMIN_ACCESS
)
require_admin_permission = require_permission(Permission.ADMIN_ACCESS)
require_tenant_management = require_permission(Permission.MANAGE_TENANT)
require_user_management = require_permission(Permission.MANAGE_USERS)
require_subscription_management = require_permission(Permission.MANAGE_SUBSCRIPTION)
require_metrics_access = require_permission(Permission.READ_METRICS)

# Convenience dependencies for common role checks
require_partner_or_above = require_role([UserRole.SUPERADMIN, UserRole.PARTNER])
require_attorney_or_above = require_role([
    UserRole.SUPERADMIN, UserRole.PARTNER, UserRole.ATTORNEY
])
require_staff_or_above = require_role([
    UserRole.SUPERADMIN, UserRole.PARTNER, UserRole.ATTORNEY, 
    UserRole.PARALEGAL, UserRole.STAFF
])
