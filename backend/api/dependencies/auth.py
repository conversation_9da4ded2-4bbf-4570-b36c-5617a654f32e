"""
Authentication dependencies for API endpoints.

This module provides FastAPI dependencies for authentication and authorization.
"""

import os
import uuid
import logging
from typing import Optional, Dict, Any

import jwt
from fastapi import Header, HTTPException, status
from pydantic import BaseModel

logger = logging.getLogger(__name__)


class UserContext(BaseModel):
    """User context for authenticated requests."""

    id: uuid.UUID
    email: str
    name: str
    tenant_id: uuid.UUID
    role: str = "user"


def _get_jwt_secret() -> str:
    """Get JWT secret from environment with validation."""
    jwt_secret = os.getenv("SUPABASE_JWT_SECRET")

    if not jwt_secret:
        app_env = os.getenv("APP_ENV", "development")
        if app_env == "production":
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="JWT secret not configured for production environment"
            )
        else:
            logger.error("SUPABASE_JWT_SECRET not configured - authentication will fail")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="JWT secret not configured"
            )

    # Validate secret length for security
    if len(jwt_secret) < 32:
        logger.error("SUPABASE_JWT_SECRET must be at least 32 characters long for security")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="JWT secret configuration error"
        )

    return jwt_secret


def _verify_jwt_token(token: str) -> Dict[str, Any]:
    """
    Verify JWT token and return payload.

    Args:
        token: JWT token to verify

    Returns:
        Dict containing token payload

    Raises:
        HTTPException: If token verification fails
    """
    try:
        jwt_secret = _get_jwt_secret()

        # Decode and verify the token
        payload = jwt.decode(
            token,
            jwt_secret,
            algorithms=["HS256"],
            options={"verify_signature": True},
        )

        return payload

    except jwt.ExpiredSignatureError:
        logger.warning("JWT token has expired")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.InvalidTokenError as e:
        logger.warning(f"Invalid JWT token: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        logger.error(f"JWT verification error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication error"
        )


async def get_current_tenant(
    x_tenant_id: Optional[str] = Header(None, alias="X-Tenant-ID"),
    authorization: Optional[str] = Header(None, alias="Authorization")
) -> uuid.UUID:
    """
    Get the current tenant ID from request headers.

    This dependency validates the tenant ID and ensures proper authentication.

    Args:
        x_tenant_id: Tenant ID from X-Tenant-ID header
        authorization: Authorization header with Bearer token

    Returns:
        UUID: The validated tenant ID

    Raises:
        HTTPException: If authentication or tenant validation fails
    """
    # Check for required headers
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing or invalid authorization header",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if not x_tenant_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="X-Tenant-ID header is required"
        )

    # Validate tenant ID format
    try:
        tenant_uuid = uuid.UUID(x_tenant_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid tenant ID format"
        )

    # Extract and verify JWT token
    token = authorization.replace("Bearer ", "")
    payload = _verify_jwt_token(token)

    # Extract tenant ID from token
    token_tenant_id = payload.get("tenant_id")
    if not token_tenant_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token does not contain tenant information"
        )

    # Verify the requested tenant matches the token tenant
    try:
        token_tenant_uuid = uuid.UUID(token_tenant_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid tenant ID in token"
        )

    if tenant_uuid != token_tenant_uuid:
        logger.warning(
            f"Tenant ID mismatch: requested {tenant_uuid}, token contains {token_tenant_uuid}"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied: tenant mismatch"
        )

    return tenant_uuid


async def get_current_user(
    authorization: str = Header(..., alias="Authorization")
) -> UserContext:
    """
    Get the current authenticated user.

    This dependency validates the JWT token and returns user context.

    Args:
        authorization: Authorization header with Bearer token

    Returns:
        UserContext: The authenticated user's context

    Raises:
        HTTPException: If authentication fails
    """
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing or invalid authorization header",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Extract and verify JWT token
    token = authorization.replace("Bearer ", "")
    payload = _verify_jwt_token(token)

    # Extract user information from token
    user_id = payload.get("sub")
    email = payload.get("email")
    tenant_id = payload.get("tenant_id")
    role = payload.get("role", "user")
    name = payload.get("name") or payload.get("user_metadata", {}).get("full_name") or email

    # Validate required fields
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token missing user ID"
        )

    if not email:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token missing email"
        )

    if not tenant_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token missing tenant ID"
        )

    # Convert string IDs to UUIDs
    try:
        user_uuid = uuid.UUID(user_id)
        tenant_uuid = uuid.UUID(tenant_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid ID format in token"
        )

    return UserContext(
        id=user_uuid,
        email=email,
        name=name,
        tenant_id=tenant_uuid,
        role=role
    )


async def require_super_admin(
    authorization: str = Header(..., alias="Authorization")
) -> dict:
    """
    Require super admin authentication for sensitive endpoints.

    This dependency validates that the user has super admin privileges.

    Args:
        authorization: Authorization header with Bearer token

    Returns:
        dict: Super admin context

    Raises:
        HTTPException: If authentication fails or user is not super admin
    """
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing or invalid authorization header",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Extract and verify JWT token
    token = authorization.replace("Bearer ", "")
    payload = _verify_jwt_token(token)

    # Extract user information
    user_id = payload.get("sub")
    email = payload.get("email")
    role = payload.get("role")
    is_super_admin = payload.get("is_super_admin", False)

    # Validate required fields
    if not user_id or not email:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token: missing user information"
        )

    # Check for super admin privileges
    # Super admin can be determined by:
    # 1. Explicit is_super_admin claim in JWT
    # 2. Role is 'super_admin'
    # 3. Email is in the super admin list
    super_admin_emails = [
        "<EMAIL>",
        "<EMAIL>"
    ]

    is_authorized = (
        is_super_admin or
        role == "super_admin" or
        email in super_admin_emails
    )

    if not is_authorized:
        logger.warning(f"Super admin access denied for user {email}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Super admin privileges required"
        )

    logger.info(f"Super admin access granted for user {email}")

    return {
        "user_id": user_id,
        "email": email,
        "role": "super_admin",
        "permissions": ["metrics:read", "deployment:control", "admin:all"]
    }
