#!/usr/bin/env python3
"""
Test script for authentication dependencies.

This script tests the JWT authentication dependencies to ensure they work correctly
after removing mock authentication.
"""

import os
import sys
import jwt
import uuid
from datetime import datetime, <PERSON><PERSON><PERSON>

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from backend.api.dependencies.auth import _verify_jwt_token, _get_jwt_secret


def create_test_jwt(payload: dict, secret: str) -> str:
    """Create a test JWT token."""
    return jwt.encode(payload, secret, algorithm="HS256")


def test_jwt_secret_validation():
    """Test JWT secret validation."""
    print("Testing JWT secret validation...")
    
    # Test with missing secret
    original_secret = os.environ.get("SUPABASE_JWT_SECRET")
    if "SUPABASE_JWT_SECRET" in os.environ:
        del os.environ["SUPABASE_JWT_SECRET"]
    
    try:
        _get_jwt_secret()
        print("❌ Should have failed with missing secret")
        return False
    except Exception as e:
        print(f"✅ Correctly failed with missing secret: {e}")
    
    # Test with short secret
    os.environ["SUPABASE_JWT_SECRET"] = "short"
    try:
        _get_jwt_secret()
        print("❌ Should have failed with short secret")
        return False
    except Exception as e:
        print(f"✅ Correctly failed with short secret: {e}")
    
    # Test with valid secret
    test_secret = "a" * 32  # 32 character secret
    os.environ["SUPABASE_JWT_SECRET"] = test_secret
    try:
        secret = _get_jwt_secret()
        if secret == test_secret:
            print("✅ Valid secret accepted")
        else:
            print("❌ Secret mismatch")
            return False
    except Exception as e:
        print(f"❌ Valid secret rejected: {e}")
        return False
    
    # Restore original secret
    if original_secret:
        os.environ["SUPABASE_JWT_SECRET"] = original_secret
    elif "SUPABASE_JWT_SECRET" in os.environ:
        del os.environ["SUPABASE_JWT_SECRET"]
    
    return True


def test_jwt_verification():
    """Test JWT token verification."""
    print("\nTesting JWT token verification...")
    
    # Set up test secret
    test_secret = "a" * 32
    os.environ["SUPABASE_JWT_SECRET"] = test_secret
    
    # Test valid token
    payload = {
        "sub": str(uuid.uuid4()),
        "email": "<EMAIL>",
        "tenant_id": str(uuid.uuid4()),
        "role": "user",
        "exp": datetime.utcnow() + timedelta(hours=1)
    }
    
    token = create_test_jwt(payload, test_secret)
    
    try:
        decoded = _verify_jwt_token(token)
        print("✅ Valid token verified successfully")
        
        # Check payload
        if decoded["sub"] == payload["sub"] and decoded["email"] == payload["email"]:
            print("✅ Token payload correctly decoded")
        else:
            print("❌ Token payload mismatch")
            return False
            
    except Exception as e:
        print(f"❌ Valid token rejected: {e}")
        return False
    
    # Test expired token
    expired_payload = {
        "sub": str(uuid.uuid4()),
        "email": "<EMAIL>",
        "exp": datetime.utcnow() - timedelta(hours=1)  # Expired
    }
    
    expired_token = create_test_jwt(expired_payload, test_secret)
    
    try:
        _verify_jwt_token(expired_token)
        print("❌ Expired token should have been rejected")
        return False
    except Exception as e:
        print(f"✅ Expired token correctly rejected: {e}")
    
    # Test invalid signature
    invalid_token = create_test_jwt(payload, "wrong_secret")
    
    try:
        _verify_jwt_token(invalid_token)
        print("❌ Invalid signature should have been rejected")
        return False
    except Exception as e:
        print(f"✅ Invalid signature correctly rejected: {e}")
    
    return True


def main():
    """Run all tests."""
    print("🔐 Testing Authentication Dependencies")
    print("=" * 50)
    
    tests = [
        test_jwt_secret_validation,
        test_jwt_verification,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ {test.__name__} failed")
        except Exception as e:
            print(f"❌ {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("💥 Some tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
